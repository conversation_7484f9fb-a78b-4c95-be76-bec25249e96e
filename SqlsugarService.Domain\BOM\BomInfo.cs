﻿using SqlSugar;
using SqlsugarService.Domain.Common;
using SqlsugarService.Domain.Craftsmanship;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Domain.BOM
{
    /// <summary>
    /// BOM信息表
    /// </summary>
    [SugarTable("BomInfo")]
    public class BomInfo : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// BOM编号
        /// </summary>
        public string BomNumber { get; set; }

        /// <summary>
        /// 是否系统编号
        /// </summary>
        public bool IsSystemNumber { get; set; }

        /// <summary>
        /// 是否默认BOM
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// BOM版本
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// 产品Id
        /// </summary>
        public decimal ProductId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 颜色代码/规格型号
        /// </summary>
        public string? ColorCode { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 日产量
        /// </summary>
        public decimal DailyOutput { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdatedTime { get; set; }

        /// <summary>
        /// 工艺路线Id（指定本BOM采用的工艺路线）
        /// </summary>
        public Guid ProcessRouteId { get; set; }

        /// <summary>
        /// 工艺路线对象（导航属性）
        /// </summary>
        public ProcessRouteEntity ProcessRoute { get; set; }

        // 导航属性
        /// <summary>
        /// BOM明细列表
        /// </summary>
        public ICollection<BomItem> BomItems { get; set; } = new List<BomItem>();
    }
}
