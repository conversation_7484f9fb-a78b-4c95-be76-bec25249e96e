using AuthService.Domain.Entities;

namespace AuthService.Application.Services;

/// <summary>
/// 动态API映射服务接口
/// 负责将服务层方法自动映射为动态API端点
/// </summary>
public interface IDynamicApiMappingService
{
    /// <summary>
    /// 初始化动态API映射
    /// 扫描服务层方法并创建对应的API端点配置
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的API端点数量</returns>
    Task<int> InitializeMappingsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 为指定服务创建动态API端点
    /// </summary>
    /// <typeparam name="TService">服务类型</typeparam>
    /// <param name="servicePrefix">API路径前缀</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的端点列表</returns>
    Task<IEnumerable<DynamicApiEndpoint>> CreateServiceEndpointsAsync<TService>(
        string servicePrefix, 
        CancellationToken cancellationToken = default) where TService : class;

    /// <summary>
    /// 刷新动态API映射
    /// 重新扫描服务并更新API端点配置
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新的端点数量</returns>
    Task<int> RefreshMappingsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有动态API端点
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>API端点列表</returns>
    Task<IEnumerable<DynamicApiEndpoint>> GetAllEndpointsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据路径和方法查找API端点
    /// </summary>
    /// <param name="path">请求路径</param>
    /// <param name="method">HTTP方法</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>匹配的API端点</returns>
    Task<DynamicApiEndpoint?> FindEndpointAsync(
        string path, 
        string method, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 启用或禁用API端点
    /// </summary>
    /// <param name="endpointId">端点ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否操作成功</returns>
    Task<bool> ToggleEndpointAsync(
        Guid endpointId, 
        bool isEnabled, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 动态API映射配置
/// </summary>
public class DynamicApiMappingOptions
{
    /// <summary>
    /// API基础路径前缀
    /// </summary>
    public string BasePrefix { get; set; } = "/api/dynamic";

    /// <summary>
    /// 是否自动创建CRUD端点
    /// </summary>
    public bool AutoCreateCrudEndpoints { get; set; } = true;

    /// <summary>
    /// 默认需要认证
    /// </summary>
    public bool RequireAuthenticationByDefault { get; set; } = true;

    /// <summary>
    /// 默认角色要求
    /// </summary>
    public List<string> DefaultRequiredRoles { get; set; } = new() { "User" };

    /// <summary>
    /// 排除的方法名称模式
    /// </summary>
    public List<string> ExcludedMethodPatterns { get; set; } = new()
    {
        "Dispose",
        "Finalize",
        "GetHashCode",
        "ToString",
        "Equals"
    };

    /// <summary>
    /// 包含的方法名称模式
    /// </summary>
    public List<string> IncludedMethodPatterns { get; set; } = new()
    {
        "Get*",
        "Create*",
        "Update*",
        "Delete*",
        "Search*",
        "List*"
    };
}

/// <summary>
/// 服务方法映射信息
/// </summary>
public class ServiceMethodMapping
{
    /// <summary>
    /// 方法名称
    /// </summary>
    public string MethodName { get; set; } = string.Empty;

    /// <summary>
    /// HTTP方法
    /// </summary>
    public string HttpMethod { get; set; } = "GET";

    /// <summary>
    /// 路径模板
    /// </summary>
    public string PathTemplate { get; set; } = string.Empty;

    /// <summary>
    /// 方法描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 参数信息
    /// </summary>
    public List<MethodParameterInfo> Parameters { get; set; } = new();

    /// <summary>
    /// 返回类型
    /// </summary>
    public string ReturnType { get; set; } = string.Empty;

    /// <summary>
    /// 是否需要认证
    /// </summary>
    public bool RequireAuthentication { get; set; } = true;

    /// <summary>
    /// 所需角色
    /// </summary>
    public List<string> RequiredRoles { get; set; } = new();
}

/// <summary>
/// 方法参数信息
/// </summary>
public class MethodParameterInfo
{
    /// <summary>
    /// 参数名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 参数类型
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 是否必需
    /// </summary>
    public bool IsRequired { get; set; } = true;

    /// <summary>
    /// 默认值
    /// </summary>
    public object? DefaultValue { get; set; }

    /// <summary>
    /// 参数来源（Query, Route, Body等）
    /// </summary>
    public string Source { get; set; } = "Query";
}
