# 更新质检服务以支持完整的关联查询
# 包括工单任务表、生产计划表、生产工单表的数据

Write-Host "=== 更新质检服务支持完整功能 ===" -ForegroundColor Green

# 1. 执行数据库迁移
Write-Host "`n🔧 步骤1: 执行数据库迁移..." -ForegroundColor Cyan
try {
    # 检查迁移脚本是否存在
    if (Test-Path "fix_database_fields.sql") {
        Write-Host "✅ 找到数据库迁移脚本" -ForegroundColor Green
        
        # 尝试通过API执行迁移
        try {
            $apiUrl = "https://localhost:7001/api/Migration/execute"
            Write-Host "正在通过API执行迁移..." -ForegroundColor Gray
            $response = Invoke-RestMethod -Uri $apiUrl -Method POST -ContentType "application/json" -SkipCertificateCheck
            Write-Host "✅ 数据库迁移成功!" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ API迁移失败，请手动执行SQL脚本" -ForegroundColor Yellow
            Write-Host "请在数据库中执行: fix_database_fields.sql" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ 未找到迁移脚本 fix_database_fields.sql" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 迁移执行失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 编译项目验证
Write-Host "`n🔧 步骤2: 编译项目验证..." -ForegroundColor Cyan
try {
    $buildResult = dotnet build EmployeeService.sln --verbosity quiet
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 项目编译成功!" -ForegroundColor Green
    } else {
        Write-Host "❌ 项目编译失败" -ForegroundColor Red
        Write-Host "请检查代码错误" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ 编译过程出错: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 测试完整功能
Write-Host "`n🔧 步骤3: 测试完整功能..." -ForegroundColor Cyan
Write-Host "现在可以测试以下完整功能:" -ForegroundColor White
Write-Host "  • 计划编号搜索 (planNumber)" -ForegroundColor Gray
Write-Host "  • 工单名称搜索 (workOrderName)" -ForegroundColor Gray
Write-Host "  • 工单编号搜索 (workOrderCode)" -ForegroundColor Gray
Write-Host "  • 任务名称搜索 (taskName)" -ForegroundColor Gray
Write-Host "  • 任务编号搜索 (taskCode)" -ForegroundColor Gray

# 4. 显示测试命令
Write-Host "`n🧪 测试命令:" -ForegroundColor Cyan
Write-Host "# 启动服务" -ForegroundColor Gray
Write-Host "dotnet run --project SqlsugarService.API" -ForegroundColor White
Write-Host "" 
Write-Host "# 测试完整功能" -ForegroundColor Gray
Write-Host ".\test_workreport_inspection_api.ps1" -ForegroundColor White

# 5. 高级搜索测试示例
Write-Host "`n📋 高级搜索测试示例:" -ForegroundColor Cyan
$testBody = @{
    pageIndex = 1
    pageSize = 10
    planNumber = "PLAN001"
    productName = "产品A"
    status = "待质检"
    workOrderName = "工单A"
    taskName = "任务1"
} | ConvertTo-Json

Write-Host "POST https://localhost:7001/api/WorkReportInspection/list" -ForegroundColor White
Write-Host "Content-Type: application/json" -ForegroundColor Gray
Write-Host ""
Write-Host $testBody -ForegroundColor Gray

Write-Host "`n=== 更新完成 ===" -ForegroundColor Green
Write-Host "质检服务现在支持完整的关联查询功能!" -ForegroundColor Cyan