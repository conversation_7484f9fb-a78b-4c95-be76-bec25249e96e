﻿using SqlsugarService.Application.DTOs.Materials;
using SqlsugarService.Application.Until;
using SqlsugarService.Domain.Materials;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.IService.Material
{
    public interface IMaterialService
    {
        // 物料分类管理
        Task<ApiResult> AddMaterialCategory(MaterialCategoryDto categoryDto);
        Task<ApiResult<List<MaterialCategoryListDto>>> GetFlatMaterialCategories();
        Task<ApiResult<List<MaterialCategoryTreeDto>>> GetMaterialCategoryTree();
        
        // 物料管理
        Task<ApiResult> AddMaterial(MaterialDto materialDto);
        Task<ApiResult<PageResult<List<MaterialSimpleDto>>>> GetMaterialsByCategory(Guid categoryId, int pageIndex, int pageSize);
    }
}
