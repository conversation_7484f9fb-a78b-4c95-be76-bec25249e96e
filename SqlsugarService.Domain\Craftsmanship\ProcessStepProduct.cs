﻿using SqlSugar;
using SqlsugarService.Domain.Common;
using SqlsugarService.Domain.Materials;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Domain.Craftsmanship
{
    /// <summary>
    /// 工艺路线-产品关联表
    /// </summary>
    public class ProcessStepProduct : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 工艺路线Id
        /// </summary>
        public Guid ProcessRouteId { get; set; }
        public ProcessRouteEntity? ProcessRoute { get; set; }

        /// <summary>
        /// 物料Id/产品Id
        /// </summary>
        public Guid MaterialId { get; set; }
        public MaterialEntity? Material { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// 用量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 损耗率
        /// </summary>
        public decimal LossRate { get; set; }

        /// <summary>
        /// 投入产出类型
        /// </summary>
        public MaterialRelationType InOutType { get; set; }
    }

    /// <summary>
    /// 投入产出类型
    /// </summary>
    public enum MaterialRelationType
    {
        /// <summary>
        /// 投入
        /// </summary>
        Input = 1,
        /// <summary>
        /// 产出（副产品或废品）
        /// </summary>
        Output = 2,
        /// <summary>
        /// 副产品
        /// </summary>
        ByProduct = 3,
        /// <summary>
        /// 废料
        /// </summary>
        Scrap = 4,
        /// <summary>
        /// 可回收物
        /// </summary>
        Recycled = 5
    }
}
