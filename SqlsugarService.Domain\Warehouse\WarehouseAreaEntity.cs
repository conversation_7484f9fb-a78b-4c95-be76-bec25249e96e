using SqlSugar;
using SqlsugarService.Domain.Common;
using System;

namespace SqlsugarService.Domain.Warehouse
{
    /// <summary>
    /// 库区实体类
    /// </summary>
    public class WarehouseAreaEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 库区名称
        /// </summary>
        public string AreaName { get; set; }

        /// <summary>
        /// 库区编码
        /// </summary>
        public string AreaCode { get; set; }

        /// <summary>
        /// 面积
        /// </summary>
        public int Area { get; set; }

        /// <summary>
        /// 所属仓库Id
        /// </summary>
        public Guid WarehouseId { get; set; }

        /// <summary>
        /// 所属仓库名称
        /// </summary>
        public string WarehouseName { get; set; }

        /// <summary>
        /// 库位数量
        /// </summary>
        public int LocationCount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }
} 