﻿using SqlsugarService.Application.Until;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.DTOs.WorkReportInspectionDto
{
    /// <summary>
    /// 获取工单检验列表搜索参数
    /// </summary>
    public class GetWorkReportInspectionSearchDto : Seach
    {
        /// <summary>
        /// 计划编号 (对应前端的"计划编号"搜索框)
        /// </summary>
        public string? PlanNumber { get; set; }

        /// <summary>
        /// 产品名称 (对应前端的"产品名称"搜索框)
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 状态 (对应前端的"状态"下拉框，如: 待质检, 已质检, 已完检)
        /// </summary>
        public string? Status { get; set; }

        // 保留原有字段以兼容现有功能
        /// <summary>
        /// 检验单号
        /// </summary>
        public string? InspectionCode { get; set; }

        /// <summary>
        /// 检验单名称
        /// </summary>
        public string? InspectionName { get; set; }

        /// <summary>
        /// 检验类型 (如：首检, 巡检, 末检)
        /// </summary>
        public string? InspectionType { get; set; }

        /// <summary>
        /// 检验部门
        /// </summary>
        public string? InspectionDepartment { get; set; }

        /// <summary>
        /// 检测结果 (如: 合格, 不合格)
        /// </summary>
        public string? OverallResult { get; set; }

        /// <summary>
        /// 报工开始时间
        /// </summary>
        public DateTime? ReportTimeStart { get; set; }

        /// <summary>
        /// 报工结束时间
        /// </summary>
        public DateTime? ReportTimeEnd { get; set; }

        /// <summary>
        /// 检验开始时间
        /// </summary>
        public DateTime? InspectionTimeStart { get; set; }

        /// <summary>
        /// 检验结束时间
        /// </summary>
        public DateTime? InspectionTimeEnd { get; set; }

        /// <summary>
        /// 产品Id
        /// </summary>
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 工序Id
        /// </summary>
        public Guid? ProcessStepId { get; set; }

        /// <summary>
        /// 站点Id
        /// </summary>
        public Guid? StationId { get; set; }

        /// <summary>
        /// 班组Id
        /// </summary>
        public Guid? TeamId { get; set; }

        /// <summary>
        /// 报工人员Id
        /// </summary>
        public Guid? ReporterId { get; set; }

        /// <summary>
        /// 检验人员Id
        /// </summary>
        public Guid? InspectorId { get; set; }
    }
}
