using SqlSugar;
using SqlsugarService.Domain.Common;
using System;

namespace SqlsugarService.Domain.Station
{
    /// <summary>
    /// 工具类型实体类
    /// </summary>
    public class ToolCategoryEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 类型名称
        /// </summary>
        public string CategoryName { get; set; }

        /// <summary>
        /// 类型编码
        /// </summary>
        public string CategoryCode { get; set; }

        /// <summary>
        /// 状态 (如: 启用, 禁用)
        /// </summary>
        public string Status { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
} 