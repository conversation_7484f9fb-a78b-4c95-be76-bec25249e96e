﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SqlsugarService.Application.DTOs.Materials;
using SqlsugarService.Application.IService.Material;
using SqlsugarService.Application.Until;
using SqlsugarService.Domain.Materials;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SqlsugarService.API.Controllers.BomRelatedController
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class MaterialController : ControllerBase
    {
        private readonly IMaterialService _materialService;

        public MaterialController(IMaterialService materialService)
        {
            _materialService = materialService;
        }

        #region 物料分类管理

        /// <summary>
        /// 添加物料分类
        /// </summary>
        /// <param name="categoryDto">物料分类信息</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ApiResult> AddMaterialCategory([FromBody] MaterialCategoryDto categoryDto)
        {
            try
            {
                return await _materialService.AddMaterialCategory(categoryDto);
            }
            catch (Exception ex)
            {
                return ApiResult.Fail($"添加物料分类失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 获取展平的物料分类列表
        /// </summary>
        /// <returns>所有物料分类列表</returns>
        [HttpGet]
        public async Task<ApiResult<List<MaterialCategoryListDto>>> GetFlatMaterialCategories()
        {
            try
            {
                return await _materialService.GetFlatMaterialCategories();
            }
            catch (Exception ex)
            {
                return ApiResult<List<MaterialCategoryListDto>>.Fail($"获取物料分类列表失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 获取树形结构的物料分类
        /// </summary>
        /// <returns>树形结构的物料分类</returns>
        [HttpGet]
        public async Task<ApiResult<List<MaterialCategoryTreeDto>>> GetMaterialCategoryTree()
        {
            try
            {
                return await _materialService.GetMaterialCategoryTree();
            }
            catch (Exception ex)
            {
                return ApiResult<List<MaterialCategoryTreeDto>>.Fail($"获取物料分类树失败: {ex.Message}", ResultCode.Error);
            }
        }

        #endregion

        #region 物料管理

        /// <summary>
        /// 添加物料
        /// </summary>
        /// <param name="materialDto">物料信息</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ApiResult> AddMaterial([FromBody] MaterialDto materialDto)
        {
            try
            {
                return await _materialService.AddMaterial(materialDto);
            }
            catch (Exception ex)
            {
                return ApiResult.Fail($"添加物料失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 按分类获取物料列表(分页)
        /// </summary>
        /// <param name="categoryId">分类ID</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">每页数量</param>
        /// <returns>分页物料列表</returns>
        [HttpGet]
        public async Task<ApiResult<PageResult<List<MaterialSimpleDto>>>> GetMaterialsByCategory(
            [FromQuery] Guid categoryId,
            [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                return await _materialService.GetMaterialsByCategory(categoryId, pageIndex, pageSize);
            }
            catch (Exception ex)
            {
                return ApiResult<PageResult<List<MaterialSimpleDto>>>.Fail(
                    $"获取物料列表失败: {ex.Message}", 
                    ResultCode.Error);
            }
        }

        #endregion
    }
}
