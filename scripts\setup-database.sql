-- PostgreSQL 数据库设置脚本
-- 用于确保用户 kong 有足够的权限创建数据库

-- 1. 连接到 postgres 数据库（作为超级用户执行）
-- psql -h localhost -U postgres -d postgres

-- 2. 检查用户是否存在
SELECT usename, usecreatedb, usesuper FROM pg_user WHERE usename = 'kong';

-- 3. 如果用户不存在，创建用户
-- CREATE USER kong WITH PASSWORD 'kong';

-- 4. 授予创建数据库的权限
ALTER USER kong CREATEDB;

-- 5. 验证权限
SELECT usename, usecreatedb, usesuper FROM pg_user WHERE usename = 'kong';

-- 6. 创建数据库（如果需要手动创建）
-- CREATE DATABASE authservice OWNER kong;

-- 7. 授予数据库权限
-- GRANT ALL PRIVILEGES ON DATABASE authservice TO kong;

-- 8. 连接到 authservice 数据库并授予 schema 权限
-- \c authservice
-- GRANT ALL ON SCHEMA public TO kong;
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO kong;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO kong;

-- 9. 设置默认权限（对未来创建的对象）
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO kong;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO kong;

-- 验证连接
-- \q

-- 测试连接命令：
-- psql -h localhost -U kong -d authservice -c "SELECT version();"
