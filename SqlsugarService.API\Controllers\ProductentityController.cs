﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SqlsugarService.Application.DTOs.PlanDto;
using SqlsugarService.Application.IService.Plan;
using SqlsugarService.Application.Until;

namespace SqlsugarService.API.Controllers
{
    /// <summary>
    /// 产品管理
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class ProductentityController : ControllerBase
    {
        private readonly IProductEntityService productEntityService;

        public ProductentityController(IProductEntityService productEntityService)
        {
            this.productEntityService = productEntityService;
        }
        /// <summary>
        /// 新增产品
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ApiResult> AddProduct(InsertupdateproductentityDto dto)
        {
            return await productEntityService.AddProduct(dto);
        }
        /// <summary>
        /// 获取产品列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ApiResult<PageResult<List<GetProductDto>>>> GetProductList([FromQuery] GetProductSearchDto search)
        {
            return await productEntityService.GetProductList(search);
        }
        /// <summary>
        /// 更新产品
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ApiResult> UpdateProduct(InsertupdateproductentityDto dto)
        {
            return await productEntityService.UpdateProduct(dto);
        }

        /// <summary>
        /// 删除产品
        /// </summary>
        [HttpPut]
        public async Task<ApiResult> DeleteProduct(Guid id)
        {
            return await productEntityService.DeleteProduct(id);
        }
    }
}
