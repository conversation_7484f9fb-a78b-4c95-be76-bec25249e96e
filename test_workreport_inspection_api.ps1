# 质检接口测试脚本
# 测试报工质检相关的API接口

$baseUrl = "https://localhost:7001/api/WorkReportInspection"

Write-Host "=== 质检接口测试开始 ===" -ForegroundColor Green

# 1. 测试获取状态选项
Write-Host "`n1. 测试获取状态选项..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/status-options" -Method GET -ContentType "application/json"
    Write-Host "状态选项获取成功:" -ForegroundColor Green
    $response.data | ConvertTo-Json -Depth 3
} catch {
    Write-Host "状态选项获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 测试获取检验类型选项
Write-Host "`n2. 测试获取检验类型选项..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/inspection-types" -Method GET -ContentType "application/json"
    Write-Host "检验类型选项获取成功:" -ForegroundColor Green
    $response.data | ConvertTo-Json -Depth 3
} catch {
    Write-Host "检验类型选项获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 测试获取检测结果选项
Write-Host "`n3. 测试获取检测结果选项..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/result-options" -Method GET -ContentType "application/json"
    Write-Host "检测结果选项获取成功:" -ForegroundColor Green
    $response.data | ConvertTo-Json -Depth 3
} catch {
    Write-Host "检测结果选项获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 测试简单列表接口
Write-Host "`n4. 测试简单列表接口..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/simple-list" -Method GET -ContentType "application/json"
    Write-Host "简单列表获取成功:" -ForegroundColor Green
    Write-Host "返回记录数: $($response.data.Count)"
} catch {
    Write-Host "简单列表获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 测试检查关联数据
Write-Host "`n5. 测试检查关联数据..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/check-related-data" -Method GET -ContentType "application/json"
    Write-Host "关联数据检查成功:" -ForegroundColor Green
    $response.data | ConvertTo-Json -Depth 3
} catch {
    Write-Host "关联数据检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 测试高级搜索接口 (POST /list)
Write-Host "`n6. 测试高级搜索接口..." -ForegroundColor Yellow
$searchBody = @{
    pageIndex = 1
    pageSize = 10
    planNumber = ""
    productName = ""
    status = ""
    inspectionCode = ""
    inspectionName = ""
    inspectionType = ""
    inspectionDepartment = ""
    overallResult = ""
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/list" -Method POST -Body $searchBody -ContentType "application/json"
    Write-Host "高级搜索成功:" -ForegroundColor Green
    Write-Host "总记录数: $($response.data.totalCount)"
    Write-Host "总页数: $($response.data.totalPage)"
    Write-Host "当前页记录数: $($response.data.data.Count)"
} catch {
    Write-Host "高级搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. 测试简单分页接口 (GET /simple-list-paged)
Write-Host "`n7. 测试简单分页接口..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/simple-list-paged?pageIndex=1&pageSize=5" -Method GET -ContentType "application/json"
    Write-Host "简单分页成功:" -ForegroundColor Green
    Write-Host "总记录数: $($response.data.totalCount)"
    Write-Host "总页数: $($response.data.totalPage)"
    Write-Host "当前页记录数: $($response.data.data.Count)"
} catch {
    Write-Host "简单分页失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 质检接口测试完成 ===" -ForegroundColor Green
Write-Host "请检查上述测试结果，确认接口是否正常工作。" -ForegroundColor Cyan