﻿using SqlSugar;
using SqlsugarService.Domain.Common;
using SqlsugarService.Domain.Craftsmanship;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Domain.Materials
{
    /// <summary>
    /// 工序-物料关联表
    /// </summary>
    public class ProcessStepMaterial : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 工序Id
        /// </summary>
        public Guid ProcessStepId { get; set; }
        [SqlSugar.SugarColumn(IsIgnore = true)]
        public ProcessStep? ProcessStep { get; set; }

        /// <summary>
        /// 物料Id
        /// </summary>
        public Guid MaterialId { get; set; }
        [SqlSugar.SugarColumn(IsIgnore = true)]
        public MaterialEntity? Material { get; set; }
    }
}
