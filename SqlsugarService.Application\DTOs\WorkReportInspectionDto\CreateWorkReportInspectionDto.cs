using System;
using System.ComponentModel.DataAnnotations;

namespace SqlsugarService.Application.DTOs.WorkReportInspectionDto
{
    /// <summary>
    /// 创建报工质检记录DTO
    /// </summary>
    public class CreateWorkReportInspectionDto
    {
        #region 基础信息
        /// <summary>
        /// 检验单号
        /// </summary>
        [Required(ErrorMessage = "检验单号不能为空")]
        [StringLength(50, ErrorMessage = "检验单号长度不能超过50个字符")]
        public string InspectionCode { get; set; }

        /// <summary>
        /// 检验单名称
        /// </summary>
        [Required(ErrorMessage = "检验单名称不能为空")]
        [StringLength(100, ErrorMessage = "检验单名称长度不能超过100个字符")]
        public string InspectionName { get; set; }

        /// <summary>
        /// 检验类型 (如：首检, 巡检, 末检)
        /// </summary>
        [Required(ErrorMessage = "检验类型不能为空")]
        [StringLength(20, ErrorMessage = "检验类型长度不能超过20个字符")]
        public string InspectionType { get; set; }

        /// <summary>
        /// 状态 (如: 未质检, 已质检, 已完检)
        /// </summary>
        [StringLength(20, ErrorMessage = "状态长度不能超过20个字符")]
        public string Status { get; set; } = "未质检";
        #endregion

        #region 关联外键
        /// <summary>
        /// 产品Id
        /// </summary>
        [Required(ErrorMessage = "产品Id不能为空")]
        public Guid ProductId { get; set; }

        /// <summary>
        /// 工序Id
        /// </summary>
        [Required(ErrorMessage = "工序Id不能为空")]
        public Guid ProcessStepId { get; set; }

        /// <summary>
        /// 站点Id
        /// </summary>
        [Required(ErrorMessage = "站点Id不能为空")]
        public Guid StationId { get; set; }

        /// <summary>
        /// 班组Id
        /// </summary>
        [Required(ErrorMessage = "班组Id不能为空")]
        public Guid TeamId { get; set; }
        
        /// <summary>
        /// 报工人员Id
        /// </summary>
        [Required(ErrorMessage = "报工人员Id不能为空")]
        public Guid ReporterId { get; set; }

        /// <summary>
        /// 检验人员Id
        /// </summary>
        public Guid? InspectorId { get; set; }

        /// <summary>
        /// 工单Id
        /// </summary>
        public Guid? WorkOrderId { get; set; }

        /// <summary>
        /// 任务Id
        /// </summary>
        public Guid? TaskId { get; set; }

        #endregion

        #region 报工信息
        /// <summary>
        /// 报工数量
        /// </summary>
        [Required(ErrorMessage = "报工数量不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "报工数量必须大于0")]
        public int ReportedQuantity { get; set; }

        /// <summary>
        /// 报工时间
        /// </summary>
        [Required(ErrorMessage = "报工时间不能为空")]
        public DateTime ReportTime { get; set; } = DateTime.Now;
        #endregion

        #region 质检信息
        /// <summary>
        /// 检验时间
        /// </summary>
        public DateTime? InspectionTime { get; set; }

        /// <summary>
        /// 检验部门
        /// </summary>
        [StringLength(100, ErrorMessage = "检验部门长度不能超过100个字符")]
        public string? InspectionDepartment { get; set; }

        /// <summary>
        /// 检测数量
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "检测数量不能为负数")]
        public int? TestedQuantity { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "合格数量不能为负数")]
        public int? QualifiedQuantity { get; set; }
        
        /// <summary>
        /// 不合格数量
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "不合格数量不能为负数")]
        public int? UnqualifiedQuantity { get; set; }

        /// <summary>
        /// 检测结果 (如: 合格, 不合格)
        /// </summary>
        [StringLength(20, ErrorMessage = "检测结果长度不能超过20个字符")]
        public string? OverallResult { get; set; }
        #endregion
        
        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remark { get; set; }
    }
}
