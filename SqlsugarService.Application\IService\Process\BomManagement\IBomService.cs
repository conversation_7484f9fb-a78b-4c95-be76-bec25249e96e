﻿using SqlsugarService.Application.DTOs.Process;
using SqlsugarService.Application.Until;
using SqlsugarService.Domain.Craftsmanship;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SqlsugarService.Application.IService.Process.BomManagement
{
    /// <summary>
    /// BOM管理服务接口
    /// 负责BOM（物料清单）的管理功能
    /// </summary>
    public interface IBomService
    {
        /// <summary>
        /// 新增BOM
        /// </summary>
        /// <param name="dto">BOM数据传输对象</param>
        /// <returns>操作结果</returns>
        Task<ApiResult> AddBomAsync(BomDto dto);

        /// <summary>
        /// 分页获取BOM列表
        /// </summary>
        /// <param name="productName">产品名称过滤（可选）</param>
        /// <param name="bomNumber">BOM编号过滤（可选）</param>
        /// <param name="pageIndex">页码（从1开始）</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>分页的BOM列表</returns>
        Task<ApiResult<PageResult<List<BomListDto>>>> GetBomPagedAsync(
            string? productName, string? bomNumber, int pageIndex, int pageSize);

        /// <summary>
        /// 根据ID获取BOM详情
        /// </summary>
        /// <param name="bomId">BOM ID</param>
        /// <returns>BOM详情</returns>
        Task<ApiResult<BomListDto>> GetBomByIdAsync(Guid bomId);

        /// <summary>
        /// 为BOM添加物料明细
        /// </summary>
        /// <param name="bomId">BOM ID</param>
        /// <param name="itemDto">物料明细数据</param>
        /// <returns>操作结果</returns>
        Task<ApiResult> AddBomItemAsync(Guid bomId, BomItemDto itemDto);

        /// <summary>
        /// 批量为BOM添加物料明细
        /// </summary>
        /// <param name="bomId">BOM ID</param>
        /// <param name="itemDtos">物料明细数据列表</param>
        /// <returns>操作结果</returns>
        Task<ApiResult> BatchAddBomItemsAsync(Guid bomId, List<BomItemDto> itemDtos);

        /// <summary>
        /// 分页获取BOM物料明细列表
        /// </summary>
        /// <param name="bomId">BOM ID</param>
        /// <param name="inOutType">投入产出类型过滤（可选）</param>
        /// <param name="pageIndex">页码（从1开始）</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>分页的BOM物料明细列表</returns>
        Task<ApiResult<PageResult<List<BomItemListDto>>>> GetBomItemsPagedAsync(
            Guid bomId, MaterialRelationType? inOutType, int pageIndex, int pageSize);
    }
}
