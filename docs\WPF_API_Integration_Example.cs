using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace YourWpfApp.Services
{
    /// <summary>
    /// API服务基类
    /// </summary>
    public class ApiService
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;

        public ApiService()
        {
            _httpClient = new HttpClient();
            _baseUrl = "http://localhost:64922/api"; // 您的API服务器地址
            
            // 设置默认请求头
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
        }

        /// <summary>
        /// GET请求
        /// </summary>
        protected async Task<T> GetAsync<T>(string endpoint)
        {
            try
            {
                var response = await _httpClient.GetAsync($"{_baseUrl}/{endpoint}");
                response.EnsureSuccessStatusCode();
                
                var json = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<T>(json);
            }
            catch (Exception ex)
            {
                throw new Exception($"GET请求失败: {ex.Message}");
            }
        }

        /// <summary>
        /// POST请求
        /// </summary>
        protected async Task<T> PostAsync<T>(string endpoint, object data)
        {
            try
            {
                var json = JsonConvert.SerializeObject(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync($"{_baseUrl}/{endpoint}", content);
                response.EnsureSuccessStatusCode();
                
                var responseJson = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<T>(responseJson);
            }
            catch (Exception ex)
            {
                throw new Exception($"POST请求失败: {ex.Message}");
            }
        }

        /// <summary>
        /// PUT请求
        /// </summary>
        protected async Task<T> PutAsync<T>(string endpoint, object data)
        {
            try
            {
                var json = JsonConvert.SerializeObject(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PutAsync($"{_baseUrl}/{endpoint}", content);
                response.EnsureSuccessStatusCode();
                
                var responseJson = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<T>(responseJson);
            }
            catch (Exception ex)
            {
                throw new Exception($"PUT请求失败: {ex.Message}");
            }
        }

        /// <summary>
        /// DELETE请求
        /// </summary>
        protected async Task<bool> DeleteAsync(string endpoint)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"{_baseUrl}/{endpoint}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                throw new Exception($"DELETE请求失败: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// 质检API服务
    /// </summary>
    public class WorkReportInspectionApiService : ApiService
    {
        /// <summary>
        /// 获取质检记录列表
        /// </summary>
        public async Task<ApiResult<PageResult<List<WorkReportInspectionDto>>>> GetListAsync(SearchDto searchDto)
        {
            return await PostAsync<ApiResult<PageResult<List<WorkReportInspectionDto>>>>(
                "WorkReportInspection/list", searchDto);
        }

        /// <summary>
        /// 获取质检记录详情
        /// </summary>
        public async Task<ApiResult<WorkReportInspectionDto>> GetByIdAsync(Guid id)
        {
            return await GetAsync<ApiResult<WorkReportInspectionDto>>(
                $"WorkReportInspection/{id}");
        }

        /// <summary>
        /// 创建质检记录
        /// </summary>
        public async Task<ApiResult<WorkReportInspectionDto>> CreateAsync(CreateWorkReportInspectionDto createDto)
        {
            return await PostAsync<ApiResult<WorkReportInspectionDto>>(
                "WorkReportInspection", createDto);
        }

        /// <summary>
        /// 更新质检记录
        /// </summary>
        public async Task<ApiResult<WorkReportInspectionDto>> UpdateAsync(UpdateWorkReportInspectionDto updateDto)
        {
            return await PutAsync<ApiResult<WorkReportInspectionDto>>(
                "WorkReportInspection", updateDto);
        }

        /// <summary>
        /// 删除质检记录
        /// </summary>
        public async Task<bool> DeleteAsync(Guid id)
        {
            return await DeleteAsync($"WorkReportInspection/{id}");
        }

        /// <summary>
        /// 获取检验类型选项
        /// </summary>
        public async Task<ApiResult<List<string>>> GetInspectionTypesAsync()
        {
            return await GetAsync<ApiResult<List<string>>>(
                "WorkReportInspection/inspection-types");
        }

        /// <summary>
        /// 获取状态选项
        /// </summary>
        public async Task<ApiResult<List<string>>> GetStatusOptionsAsync()
        {
            return await GetAsync<ApiResult<List<string>>>(
                "WorkReportInspection/status-options");
        }

        /// <summary>
        /// 执行质检操作
        /// </summary>
        public async Task<ApiResult<WorkReportInspectionDto>> PerformInspectionAsync(Guid id, UpdateWorkReportInspectionDto updateDto)
        {
            return await PostAsync<ApiResult<WorkReportInspectionDto>>(
                $"WorkReportInspection/{id}/perform-inspection", updateDto);
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        public async Task<ApiResult<object>> GetStatisticsAsync(SearchDto searchDto)
        {
            return await PostAsync<ApiResult<object>>(
                "WorkReportInspection/statistics", searchDto);
        }
    }

    #region DTO类定义 (需要与API保持一致)
    
    public class ApiResult<T>
    {
        public T Data { get; set; }
        public bool IsSucc { get; set; }
        public int Code { get; set; }
        public string Msg { get; set; }
    }

    public class PageResult<T>
    {
        public T Data { get; set; }
        public int TotalCount { get; set; }
        public int TotalPage { get; set; }
    }

    public class WorkReportInspectionDto
    {
        public Guid Id { get; set; }
        public string InspectionCode { get; set; }
        public string InspectionName { get; set; }
        public string InspectionType { get; set; }
        public string Status { get; set; }
        
        // 关联信息
        public string ProductName { get; set; }
        public string ProductCode { get; set; }
        public string ProcessStepName { get; set; }
        public string StationName { get; set; }
        public string TeamName { get; set; }
        public string ReporterName { get; set; }
        public string InspectorName { get; set; }
        public string WorkOrderName { get; set; }
        public string TaskName { get; set; }
        
        // 质检数据
        public int ReportedQuantity { get; set; }
        public DateTime ReportTime { get; set; }
        public DateTime? InspectionTime { get; set; }
        public string InspectionDepartment { get; set; }
        public int? TestedQuantity { get; set; }
        public int? QualifiedQuantity { get; set; }
        public int? UnqualifiedQuantity { get; set; }
        public decimal? QualificationRate { get; set; }
        public string OverallResult { get; set; }
        public string Remark { get; set; }
    }

    public class CreateWorkReportInspectionDto
    {
        public string InspectionCode { get; set; }
        public string InspectionName { get; set; }
        public string InspectionType { get; set; }
        public string Status { get; set; } = "未质检";
        
        public Guid ProductId { get; set; }
        public Guid ProcessStepId { get; set; }
        public Guid StationId { get; set; }
        public Guid TeamId { get; set; }
        public Guid ReporterId { get; set; }
        public Guid? InspectorId { get; set; }
        public Guid? WorkOrderId { get; set; }
        public Guid? TaskId { get; set; }
        
        public int ReportedQuantity { get; set; }
        public DateTime ReportTime { get; set; } = DateTime.Now;
        
        public DateTime? InspectionTime { get; set; }
        public string InspectionDepartment { get; set; }
        public int? TestedQuantity { get; set; }
        public int? QualifiedQuantity { get; set; }
        public int? UnqualifiedQuantity { get; set; }
        public string OverallResult { get; set; }
        public string Remark { get; set; }
    }

    public class UpdateWorkReportInspectionDto
    {
        public Guid Id { get; set; }
        public string InspectionCode { get; set; }
        public string InspectionName { get; set; }
        public string InspectionType { get; set; }
        public string Status { get; set; }
        public Guid? InspectorId { get; set; }
        public DateTime? InspectionTime { get; set; }
        public string InspectionDepartment { get; set; }
        public int? TestedQuantity { get; set; }
        public int? QualifiedQuantity { get; set; }
        public int? UnqualifiedQuantity { get; set; }
        public string OverallResult { get; set; }
        public string Remark { get; set; }
    }

    public class SearchDto
    {
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string InspectionCode { get; set; }
        public string InspectionName { get; set; }
        public string InspectionType { get; set; }
        public string Status { get; set; }
        public string InspectionDepartment { get; set; }
        public string OverallResult { get; set; }
        public DateTime? ReportTimeStart { get; set; }
        public DateTime? ReportTimeEnd { get; set; }
        public DateTime? InspectionTimeStart { get; set; }
        public DateTime? InspectionTimeEnd { get; set; }
    }

    #endregion
}
