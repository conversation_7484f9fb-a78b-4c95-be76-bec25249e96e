﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.DTOs.WorkReportInspectionDto
{
    public class GetWorkReportInspectionDto
    {
        public Guid Id { get; set; } = Guid.NewGuid();

        #region 基础信息
        /// <summary>
        /// 检验单号
        /// </summary>
        public string InspectionCode { get; set; }

        /// <summary>
        /// 检验单名称
        /// </summary>
        public string InspectionName { get; set; }

        /// <summary>
        /// 检验类型 (如：首检, 巡检, 末检)
        /// </summary>
        public string InspectionType { get; set; }

        /// <summary>
        /// 状态 (如: 未质检, 已质检)
        /// </summary>
        public string Status { get; set; }
        #endregion

        #region 关联外键
        /// <summary>
        /// 产品Id
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// 工序Id
        /// </summary>
        public Guid ProcessStepId { get; set; }

        /// <summary>
        /// 站点Id
        /// </summary>
        public Guid StationId { get; set; }

        /// <summary>
        /// 班组Id
        /// </summary>
        public Guid TeamId { get; set; }

        /// <summary>
        /// 报工人员Id
        /// </summary>
        public Guid ReporterId { get; set; }

        /// <summary>
        /// 检验人员Id
        /// </summary>
        public Guid? InspectorId { get; set; }

        /// <summary>
        /// 任务Id
        /// </summary>
        public Guid? TaskId { get; set; }

        /// <summary>
        /// 生产计划Id
        /// </summary>
        public Guid? ProductionPlanId { get; set; }

        /// <summary>
        /// 生产工单Id
        /// </summary>
        public Guid? ProductionOrderId { get; set; }

        /// <summary>
        /// 任务负责人Id
        /// </summary>
        public Guid? TaskManagerId { get; set; }
        #endregion

        #region 关联信息显示字段 (通过导航属性计算)
        /// <summary>
        /// 生产计划编号 (来自ProductionPlan.PlanNumber)
        /// </summary>
        public string? ProductionPlanNumber { get; set; }

        /// <summary>
        /// 生产计划名称 (来自ProductionPlan.PlanName)
        /// </summary>
        public string? ProductionPlanName { get; set; }

        /// <summary>
        /// 工单编号 (来自ProductionOrder.OrderNumber)
        /// </summary>
        public string? ProductionOrderNumber { get; set; }

        /// <summary>
        /// 工单名称 (来自ProductionOrder.OrderName)
        /// </summary>
        public string? ProductionOrderName { get; set; }

        /// <summary>
        /// 任务名称 (来自Task.TaskName)
        /// </summary>
        public string? TaskName { get; set; }

        /// <summary>
        /// 任务编号 (来自Task.TaskNumber)
        /// </summary>
        public string? TaskCode { get; set; }

        /// <summary>
        /// 工艺路线名称 (来自ProcessRoute.RouteName)
        /// </summary>
        public string? ProcessRouteName { get; set; }

        /// <summary>
        /// 站点名称 (来自Station.StationName)
        /// </summary>
        public string? StationName { get; set; }

        /// <summary>
        /// 工序名称 (来自ProcessStep.ProcessStepName)
        /// </summary>
        public string? ProcessStepName { get; set; }

        /// <summary>
        /// 产品名称 (来自Product.MaterialName)
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 产品编号 (来自Product.MaterialNumber)
        /// </summary>
        public string? ProductCode { get; set; }

        /// <summary>
        /// 计划编号 (关联生产计划的编号)
        /// </summary>
        public string? PlanNumber { get; set; }

        /// <summary>
        /// 计划名称 (关联生产计划的名称)
        /// </summary>
        public string? PlanName { get; set; }

        /// <summary>
        /// 班组名称 (来自Team.TeamName)
        /// </summary>
        public string? TeamName { get; set; }

        /// <summary>
        /// 报工人员姓名 (来自Reporter.DisplayName)
        /// </summary>
        public string? ReporterName { get; set; }

        /// <summary>
        /// 检验人员姓名 (来自Inspector.DisplayName)
        /// </summary>
        public string? InspectorName { get; set; }
        #endregion

        #region 报工信息
        /// <summary>
        /// 报工数量
        /// </summary>
        public int ReportedQuantity { get; set; }

        /// <summary>
        /// 报工时间
        /// </summary>
        public DateTime ReportTime { get; set; }
        #endregion

        #region 质检信息
        /// <summary>
        /// 检验时间
        /// </summary>
        public DateTime? InspectionTime { get; set; }

        /// <summary>
        /// 检验部门
        /// </summary>
        public string? InspectionDepartment { get; set; }

        /// <summary>
        /// 检测数量
        /// </summary>
        public int? TestedQuantity { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        public int? QualifiedQuantity { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        public int? UnqualifiedQuantity { get; set; }

        /// <summary>
        /// 合格率 (计算字段，百分比)
        /// </summary>
        public decimal? QualificationRate
        {
            get
            {
                if (TestedQuantity.HasValue && TestedQuantity.Value > 0 && QualifiedQuantity.HasValue)
                {
                    return Math.Round((decimal)QualifiedQuantity.Value / TestedQuantity.Value * 100, 2);
                }
                return null;
            }
        }

        /// <summary>
        /// 检测结果 (如: 合格, 不合格)
        /// </summary>
        public string? OverallResult { get; set; }
        #endregion

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }
}
