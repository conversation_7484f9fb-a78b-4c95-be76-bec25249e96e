<Window x:Class="YourWpfApp.Views.InspectionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="质检管理" Height="600" Width="1200">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10">
            <TextBox x:Name="SearchTextBox" 
                     Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                     Width="200" Height="30" Margin="0,0,10,0"
                     VerticalContentAlignment="Center"
                     Watermark="搜索检验单名称..."/>
            
            <Button Content="搜索" Command="{Binding SearchCommand}" 
                    Width="80" Height="30" Margin="0,0,10,0"/>
            
            <Button Content="刷新" Command="{Binding LoadDataCommand}" 
                    Width="80" Height="30" Margin="0,0,10,0"/>
            
            <Button Content="新建" Command="{Binding CreateCommand}" 
                    Width="80" Height="30" Margin="0,0,10,0"/>
            
            <Button Content="编辑" Command="{Binding UpdateCommand}" 
                    Width="80" Height="30" Margin="0,0,10,0"/>
            
            <Button Content="删除" Command="{Binding DeleteCommand}" 
                    Width="80" Height="30" Margin="0,0,10,0"/>
            
            <Button Content="执行质检" Command="{Binding PerformInspectionCommand}" 
                    Width="80" Height="30" Margin="0,0,10,0"/>
        </StackPanel>

        <!-- 数据表格 -->
        <DataGrid Grid.Row="1" 
                  ItemsSource="{Binding Inspections}"
                  SelectedItem="{Binding SelectedInspection}"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  IsReadOnly="True"
                  GridLinesVisibility="All"
                  HeadersVisibility="All"
                  Margin="10">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="序号" Width="60">
                    <DataGridTextColumn.Binding>
                        <Binding RelativeSource="{RelativeSource AncestorType=DataGridRow}" 
                                 Converter="{StaticResource RowNumberConverter}"/>
                    </DataGridTextColumn.Binding>
                </DataGridTextColumn>
                
                <DataGridTextColumn Header="检验单名称" Binding="{Binding InspectionName}" Width="120"/>
                <DataGridTextColumn Header="检验单号" Binding="{Binding InspectionCode}" Width="120"/>
                <DataGridTextColumn Header="工单名称" Binding="{Binding WorkOrderName}" Width="100"/>
                <DataGridTextColumn Header="任务名称" Binding="{Binding TaskName}" Width="100"/>
                <DataGridTextColumn Header="站点名称" Binding="{Binding StationName}" Width="80"/>
                <DataGridTextColumn Header="工序名称" Binding="{Binding ProcessStepName}" Width="80"/>
                <DataGridTextColumn Header="产品名称" Binding="{Binding ProductName}" Width="100"/>
                <DataGridTextColumn Header="产品编号" Binding="{Binding ProductCode}" Width="100"/>
                <DataGridTextColumn Header="班组名称" Binding="{Binding TeamName}" Width="80"/>
                <DataGridTextColumn Header="报工人员" Binding="{Binding ReporterName}" Width="80"/>
                <DataGridTextColumn Header="报工数量" Binding="{Binding ReportedQuantity}" Width="80"/>
                <DataGridTextColumn Header="报工时间" Binding="{Binding ReportTime, StringFormat=yyyy-MM-dd HH:mm}" Width="120"/>
                <DataGridTextColumn Header="检验时间" Binding="{Binding InspectionTime, StringFormat=yyyy-MM-dd HH:mm}" Width="120"/>
                <DataGridTextColumn Header="检验类型" Binding="{Binding InspectionType}" Width="80"/>
                <DataGridTextColumn Header="检验部门" Binding="{Binding InspectionDepartment}" Width="80"/>
                <DataGridTextColumn Header="检验人" Binding="{Binding InspectorName}" Width="80"/>
                <DataGridTextColumn Header="检测数量" Binding="{Binding TestedQuantity}" Width="80"/>
                <DataGridTextColumn Header="合格数量" Binding="{Binding QualifiedQuantity}" Width="80"/>
                <DataGridTextColumn Header="不合格数量" Binding="{Binding UnqualifiedQuantity}" Width="80"/>
                <DataGridTextColumn Header="合格率" Binding="{Binding QualificationRate, StringFormat=P2}" Width="80"/>
                <DataGridTextColumn Header="检测结果" Binding="{Binding OverallResult}" Width="80"/>
                <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="80"/>
                <DataGridTextColumn Header="备注" Binding="{Binding Remark}" Width="120"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="{Binding Inspections.Count, StringFormat=共 {0} 条记录}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <ProgressBar Width="100" Height="16" 
                             IsIndeterminate="{Binding IsLoading}"
                             Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
