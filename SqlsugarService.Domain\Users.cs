﻿using SqlsugarService.Domain.Common;
using SqlSugar;
namespace SqlsugarService.Domain
{
    [SugarTable("user")] // 指定表名为 user
    public class Users: BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }=Guid.NewGuid();
        /// <summary>
        /// 用户名（唯一标识）
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱地址（唯一标识）
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 密码哈希值 (Password Hash)
        ///
        /// 作用：存储用户密码的加密后的值，而不是明文密码
        ///
        /// 工作原理：
        /// 1. 用户注册时：明文密码 + 盐值 → 哈希算法 → 哈希值（存储到数据库）
        /// 2. 用户登录时：输入密码 + 存储的盐值 → 哈希算法 → 新哈希值 → 与存储的哈希值比较
        ///
        /// 安全性：
        /// - 即使数据库泄露，攻击者也无法直接获得用户的明文密码
        /// - 哈希算法是单向的，理论上无法从哈希值反推出原始密码
        ///
        /// 示例：
        /// 明文密码: "password123"
        /// 盐值: "randomSalt456"
        /// 哈希值: "a1b2c3d4e5f6..." (SHA256等算法计算结果)
        /// </summary>
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// 密码盐值 (Password Salt)
        ///
        /// 作用：防止彩虹表攻击和相同密码产生相同哈希值的问题
        ///
        /// 工作原理：
        /// 1. 每个用户都有唯一的随机盐值
        /// 2. 密码哈希时：Hash(密码 + 盐值) = 哈希值
        /// 3. 即使两个用户使用相同密码，由于盐值不同，哈希值也不同
        ///
        /// 安全性优势：
        /// - 防止彩虹表攻击：攻击者无法使用预计算的哈希表破解密码
        /// - 防止相同密码识别：相同密码在数据库中显示为不同的哈希值
        /// - 增加破解难度：攻击者需要为每个用户单独计算哈希值
        ///
        /// 示例：
        /// 用户A: 密码"123456" + 盐值"salt1" = 哈希值"abc123..."
        /// 用户B: 密码"123456" + 盐值"salt2" = 哈希值"def456..."
        ///
        /// 注意：盐值通常以Base64编码格式存储
        /// </summary>
        public string PasswordSalt { get; set; } = string.Empty;

        /// <summary>
        /// 用户显示名称
        /// </summary>
        public string? DisplayName { get; set; }
    }
}
