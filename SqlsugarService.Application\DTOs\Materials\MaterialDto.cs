using SqlsugarService.Domain.Materials;
using System;
using System.ComponentModel.DataAnnotations;

namespace SqlsugarService.Application.DTOs.Materials
{
    /// <summary>
    /// 物料数据传输对象
    /// </summary>
    public class MaterialDto
    {
        /// <summary>
        /// 物料ID（更新时使用）
        /// </summary>
        public Guid? Id { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        [Required(ErrorMessage = "物料编号不能为空")]
        [StringLength(30, ErrorMessage = "物料编号长度不能超过30个字符")]
        public string MaterialNumber { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        [Required(ErrorMessage = "物料名称不能为空")]
        [StringLength(100, ErrorMessage = "物料名称长度不能超过100个字符")]
        public string MaterialName { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        [StringLength(100, ErrorMessage = "规格型号长度不能超过100个字符")]
        public string? SpecificationModel { get; set; }

        /// <summary>
        /// 单位【个、箱、件、套、台、米、条】
        /// </summary>
        [StringLength(20, ErrorMessage = "单位长度不能超过20个字符")]
        public string? Unit { get; set; }

        /// <summary>
        /// 物料类型
        /// </summary>
        [Required(ErrorMessage = "物料类型不能为空")]
        public MaterialTypeEnum MaterialType { get; set; }

        /// <summary>
        /// 物料属性 【自制、委外、外购、其他】
        /// </summary>
        [StringLength(20, ErrorMessage = "物料属性长度不能超过20个字符")]
        public string? MaterialProperty { get; set; }

        /// <summary>
        /// 物料分类Id
        /// </summary>
        [Required(ErrorMessage = "物料分类不能为空")]
        public Guid MaterialCategoryId { get; set; }

        /// <summary>
        /// 物料分类名称（只读，用于显示）
        /// </summary>
        public string? MaterialCategoryName { get; set; }

        /// <summary>
        /// 状态 【启用、禁用】
        /// </summary>
        [StringLength(10, ErrorMessage = "状态长度不能超过10个字符")]
        public string? Status { get; set; } = "启用";

        /// <summary>
        /// 有效日期
        /// </summary>
        public DateTime? EffectiveDate { get; set; }

        /// <summary>
        /// 有效期单位
        /// </summary>
        [StringLength(10, ErrorMessage = "有效期单位长度不能超过10个字符")]
        public string? EffectiveUnit { get; set; }

        /// <summary>
        /// 报警天数【提前**天报警】
        /// </summary>
        [Range(0, 365, ErrorMessage = "报警天数必须在0-365之间")]
        public int? AlarmDays { get; set; }

        /// <summary>
        /// 库存上限
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "库存上限不能为负数")]
        public int? StockUpperLimit { get; set; }

        /// <summary>
        /// 库存下限
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "库存下限不能为负数")]
        public int? StockLowerLimit { get; set; }

        /// <summary>
        /// 采购价格
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "采购价格不能为负数")]
        public decimal? PurchasePrice { get; set; }

        /// <summary>
        /// 销售价格
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "销售价格不能为负数")]
        public decimal? SalesPrice { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remarks { get; set; }

        /// <summary>
        /// 物料图片URL
        /// </summary>
        [StringLength(500, ErrorMessage = "图片URL长度不能超过500个字符")]
        public string? MaterialImage { get; set; }

        /// <summary>
        /// 附件URL
        /// </summary>
        [StringLength(500, ErrorMessage = "附件URL长度不能超过500个字符")]
        public string? Attachment { get; set; }
    }

    /// <summary>
    /// 物料查询DTO
    /// </summary>
    public class MaterialQueryDto
    {
        /// <summary>
        /// 搜索关键词（物料编号、名称、规格型号）
        /// </summary>
        public string? Keyword { get; set; }

        /// <summary>
        /// 物料类型
        /// </summary>
        public MaterialTypeEnum? MaterialType { get; set; }

        /// <summary>
        /// 物料分类ID
        /// </summary>
        public Guid? MaterialCategoryId { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 物料属性
        /// </summary>
        public string? MaterialProperty { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "页码必须大于0")]
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        [Range(1, 100, ErrorMessage = "每页大小必须在1-100之间")]
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// 排序字段
        /// </summary>
        public string? SortBy { get; set; } = "CreatedAt";

        /// <summary>
        /// 排序方向（asc/desc）
        /// </summary>
        public string? SortDirection { get; set; } = "desc";
    }

    /// <summary>
    /// 物料简化DTO（用于下拉选择等场景）
    /// </summary>
    public class MaterialSimpleDto
    {
        /// <summary>
        /// 物料ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        public string MaterialNumber { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string MaterialName { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string? SpecificationModel { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 物料类型
        /// </summary>
        public MaterialTypeEnum MaterialType { get; set; }

        /// <summary>
        /// 物料属性 【自制、委外、外购、其他】
        /// </summary>
        public string? MaterialProperty { get; set; }

        /// <summary>
        /// 物料分类Id
        /// </summary>
        public Guid? MaterialCategoryId { get; set; }

        /// <summary>
        /// 物料分类名称
        /// </summary>
        public string? MaterialCategoryName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string? Status { get; set; }
    }
} 