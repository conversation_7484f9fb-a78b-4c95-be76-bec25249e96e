# 质检数据来源表分析

## 📊 质检数据表结构概览

质检数据主要来源于 **报工质检表 (WorkReportInspectionEntity)** 以及与其关联的多个业务表。

## 🗂️ 核心数据表

### 1. 主表：报工质检表 (WorkReportInspectionEntity)
**表名**: `workreportinspectionentity` (或 `workreportinspection`)

**存储内容**:
- ✅ 质检基础信息（检验单号、名称、类型、状态）
- ✅ 报工信息（报工数量、报工时间）
- ✅ 质检结果（检测数量、合格数量、不合格数量、检测结果）
- ✅ 关联外键（产品ID、工序ID、站点ID、班组ID、人员ID等）

## 🔗 关联数据表

### 2. 产品表 (ProductEntity)
**关联字段**: `ProductId` → `Product.Id`  
**提供数据**:
- `ProductName` ← `Product.MaterialName`
- `ProductCode` ← `Product.MaterialNumber`

### 3. 工序表 (ProcessStep)
**关联字段**: `ProcessStepId` → `ProcessStep.Id`  
**提供数据**:
- `ProcessStepName` ← `ProcessStep.ProcessStepName`

### 4. 站点表 (StationEntity)
**关联字段**: `StationId` → `Station.Id`  
**提供数据**:
- `StationName` ← `Station.StationName`

### 5. 班组表 (TeamEntity)
**关联字段**: `TeamId` → `Team.Id`  
**提供数据**:
- `TeamName` ← `Team.TeamName`

### 6. 用户表 (Users) - 报工人员
**关联字段**: `ReporterId` → `Users.Id`  
**提供数据**:
- `ReporterName` ← `Reporter.DisplayName`

### 7. 用户表 (Users) - 检验人员
**关联字段**: `InspectorId` → `Users.Id`  
**提供数据**:
- `InspectorName` ← `Inspector.DisplayName`

### 8. 工单任务表 (WorkOrderTaskEntity) 🔄
**关联字段**: `TaskId` → `Task.Id`  
**提供数据**:
- `TaskName` ← `Task.TaskName`
- `TaskCode` ← `Task.TaskNumber`
- **状态**: ⚠️ 暂时被忽略（数据库字段缺失）

### 9. 生产计划表 (ProductionPlan) 🔄
**关联字段**: `ProductionPlanId` → `ProductionPlan.Id`  
**提供数据**:
- `PlanName` ← `ProductionPlan.PlanName`
- `PlanNumber` ← `ProductionPlan.PlanNumber`
- **状态**: ⚠️ 暂时被忽略（数据库字段缺失）

### 10. 生产工单表 (ProductionOrder) 🔄
**关联字段**: `ProductionOrderId` → `ProductionOrder.Id`  
**提供数据**:
- `WorkOrderName` ← `ProductionOrder.OrderName`
- `WorkOrderCode` ← `ProductionOrder.OrderCode`
- **状态**: ⚠️ 暂时被忽略（数据库字段缺失）

## 📋 数据查询逻辑

### 当前可用的关联查询
```csharp
var query = _workReportInspectionRepository.AsQueryable()
    .Includes(x => x.Product)        // ✅ 产品信息
    .Includes(x => x.ProcessStep)    // ✅ 工序信息
    .Includes(x => x.Station)        // ✅ 站点信息
    .Includes(x => x.Team)           // ✅ 班组信息
    .Includes(x => x.Reporter)       // ✅ 报工人员信息
    .Includes(x => x.Inspector)      // ✅ 检验人员信息
    // .Includes(x => x.Task)        // ⚠️ 暂时被忽略
    // .Includes(x => x.ProductionPlan)  // ⚠️ 暂时被忽略
    // .Includes(x => x.ProductionOrder) // ⚠️ 暂时被忽略
```

### 数据转换映射
```csharp
var dto = new GetWorkReportInspectionDto
{
    // 主表数据
    Id = entity.Id,
    InspectionCode = entity.InspectionCode,
    InspectionName = entity.InspectionName,
    Status = entity.Status,
    ReportedQuantity = entity.ReportedQuantity,
    ReportTime = entity.ReportTime,
    
    // 关联表数据 ✅ 当前可用
    ProductName = entity.Product?.MaterialName,
    ProductCode = entity.Product?.MaterialNumber,
    ProcessStepName = entity.ProcessStep?.ProcessStepName,
    StationName = entity.Station?.StationName,
    TeamName = entity.Team?.TeamName,
    ReporterName = entity.Reporter?.DisplayName,
    InspectorName = entity.Inspector?.DisplayName,
    
    // 关联表数据 ⚠️ 暂时不可用
    // TaskName = entity.Task?.TaskName,
    // TaskCode = entity.Task?.TaskNumber,
    // PlanName = entity.ProductionPlan?.PlanName,
    // PlanNumber = entity.ProductionPlan?.PlanNumber,
    // WorkOrderName = entity.ProductionOrder?.OrderName,
    // WorkOrderCode = entity.ProductionOrder?.OrderCode,
};
```

## 🎯 数据流向图

```
报工质检表 (WorkReportInspectionEntity)
├── ProductId ────────→ 产品表 (ProductEntity)
├── ProcessStepId ────→ 工序表 (ProcessStep)
├── StationId ────────→ 站点表 (StationEntity)
├── TeamId ───────────→ 班组表 (TeamEntity)
├── ReporterId ───────→ 用户表 (Users) [报工人员]
├── InspectorId ──────→ 用户表 (Users) [检验人员]
├── TaskId ───────────→ 工单任务表 (WorkOrderTaskEntity) ⚠️
├── ProductionPlanId ─→ 生产计划表 (ProductionPlan) ⚠️
└── ProductionOrderId → 生产工单表 (ProductionOrder) ⚠️
```

## 📊 数据完整性状态

### ✅ 完全可用的数据源 (7个表)
1. **报工质检表** - 主表数据
2. **产品表** - 产品名称、编号
3. **工序表** - 工序名称
4. **站点表** - 站点名称
5. **班组表** - 班组名称
6. **用户表** - 报工人员、检验人员姓名

### ⚠️ 暂时不可用的数据源 (3个表)
7. **工单任务表** - 任务名称、编号
8. **生产计划表** - 计划名称、编号
9. **生产工单表** - 工单名称、编号

## 🔧 恢复完整数据源的步骤

### 1. 执行数据库迁移
添加缺失的外键字段：
```sql
ALTER TABLE workreportinspectionentity ADD COLUMN taskid UUID NULL;
ALTER TABLE workreportinspectionentity ADD COLUMN productionplanid UUID NULL;
ALTER TABLE workreportinspectionentity ADD COLUMN productionorderid UUID NULL;
```

### 2. 移除实体忽略注解
```csharp
// 移除这些注解
[SugarColumn(IsIgnore = true)]
```

### 3. 更新查询逻辑
```csharp
.Includes(x => x.Task)
.Includes(x => x.ProductionPlan)
.Includes(x => x.ProductionOrder)
```

## 📝 业务逻辑说明

### 数据生成流程
1. **报工阶段**: 员工完成生产任务后进行报工
2. **质检创建**: 基于报工数据创建质检记录
3. **质检执行**: 质检人员对报工产品进行检验
4. **结果记录**: 记录检验结果和相关数据

### 关联关系说明
- **一对一关系**: 每个质检记录对应一个产品、工序、站点等
- **多对一关系**: 多个质检记录可能对应同一个班组、人员等
- **可选关联**: 检验人员、任务、计划、工单等可能为空

## 🎯 数据查询性能考虑

### 当前查询性能
- ✅ **良好**: 6个表的关联查询性能稳定
- ✅ **索引**: 外键字段建议添加索引

### 完整功能后的性能
- ⚠️ **注意**: 9个表的关联查询可能影响性能
- 🔧 **建议**: 考虑分页查询和必要时的缓存策略

---

**总结**: 质检数据主要来源于报工质检主表及其关联的9个业务表，目前6个表的数据完全可用，3个表的数据需要数据库迁移后才能使用。