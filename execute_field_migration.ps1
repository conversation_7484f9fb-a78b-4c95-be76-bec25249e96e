# 执行质检表字段迁移脚本
# 添加缺失的 productionplanid, productionorderid, taskid 字段

Write-Host "=== 质检表字段迁移执行器 ===" -ForegroundColor Green

# 检查SQL脚本是否存在
$sqlScript = "fix_database_fields.sql"
if (-not (Test-Path $sqlScript)) {
    Write-Host "❌ 错误: 找不到迁移脚本 $sqlScript" -ForegroundColor Red
    Write-Host "请确保 fix_database_fields.sql 文件存在于当前目录" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ 找到迁移脚本: $sqlScript" -ForegroundColor Green

# 显示将要添加的字段
Write-Host "`n📋 将要添加的字段:" -ForegroundColor Cyan
Write-Host "   • productionplanid (UUID) - 生产计划ID" -ForegroundColor White
Write-Host "   • productionorderid (UUID) - 生产工单ID" -ForegroundColor White
Write-Host "   • taskid (UUID) - 任务ID" -ForegroundColor White

# 询问用户确认
Write-Host "`n⚠️  注意事项:" -ForegroundColor Yellow
Write-Host "   • 此操作将修改数据库表结构" -ForegroundColor Gray
Write-Host "   • 建议在执行前备份数据库" -ForegroundColor Gray
Write-Host "   • 确保数据库连接正常" -ForegroundColor Gray

$confirm = Read-Host "`n是否继续执行迁移? (y/N)"
if ($confirm -ne 'y' -and $confirm -ne 'Y') {
    Write-Host "❌ 用户取消操作" -ForegroundColor Yellow
    exit 0
}

# 方式1: 通过API执行迁移
Write-Host "`n🔧 方式1: 通过API执行迁移..." -ForegroundColor Cyan
try {
    $apiUrl = "https://localhost:7001/api/Migration/execute"
    Write-Host "正在调用迁移API: $apiUrl" -ForegroundColor Gray
    
    $response = Invoke-RestMethod -Uri $apiUrl -Method POST -ContentType "application/json" -SkipCertificateCheck
    Write-Host "✅ API迁移成功!" -ForegroundColor Green
    Write-Host "响应: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Gray
} catch {
    Write-Host "❌ API迁移失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "将尝试其他方式..." -ForegroundColor Yellow
}

# 方式2: 显示手动执行说明
Write-Host "`n🔧 方式2: 手动执行SQL脚本" -ForegroundColor Cyan
Write-Host "如果API方式失败，请手动执行以下步骤:" -ForegroundColor Yellow
Write-Host "1. 连接到PostgreSQL数据库" -ForegroundColor White
Write-Host "2. 执行命令: \i $sqlScript" -ForegroundColor White
Write-Host "3. 或者复制脚本内容直接在数据库中执行" -ForegroundColor White

# 显示脚本内容
Write-Host "`n📄 SQL脚本内容预览:" -ForegroundColor Cyan
try {
    $scriptContent = Get-Content $sqlScript -Raw
    Write-Host $scriptContent -ForegroundColor Gray
} catch {
    Write-Host "❌ 无法读取脚本内容: $($_.Exception.Message)" -ForegroundColor Red
}

# 迁移完成后的操作说明
Write-Host "`n✅ 迁移完成后的操作:" -ForegroundColor Green
Write-Host "1. 编辑 WorkReportInspectionEntity.cs" -ForegroundColor White
Write-Host "2. 移除以下字段的 [SugarColumn(IsIgnore = true)] 注解:" -ForegroundColor White
Write-Host "   • ProductionPlanId" -ForegroundColor Gray
Write-Host "   • ProductionOrderId" -ForegroundColor Gray
Write-Host "   • TaskId" -ForegroundColor Gray
Write-Host "3. 移除相关导航属性的忽略注解" -ForegroundColor White
Write-Host "4. 重新编译项目: dotnet build EmployeeService.sln" -ForegroundColor White
Write-Host "5. 测试完整功能" -ForegroundColor White

Write-Host "`n=== 迁移执行完成 ===" -ForegroundColor Green