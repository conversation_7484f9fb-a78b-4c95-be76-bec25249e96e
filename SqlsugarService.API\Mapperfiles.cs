﻿using AutoMapper;
using SqlsugarService.Application.DTOs.PlanDto;
using SqlsugarService.Application.DTOs.SalesDto;
using SqlsugarService.Application.DTOs.WorkReportInspectionDto;
using SqlsugarService.Domain.InventoryChange;
using SqlsugarService.Domain.Materials;
using SqlsugarService.Domain.Plan;
using SqlsugarService.Domain.QualityInspection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.API
{
    /// <summary>
    /// AutoMapper配置文件
    /// </summary>
    public class Mapperfiles:Profile
    {
        public Mapperfiles()
        {
            //销售订单新增修改
            CreateMap<insertupdatesalesorderDto, Salesorder>().ReverseMap();
            //销售订单列表
            CreateMap<getsalesorderDto, Salesorder>().ReverseMap();
            //产品列表
            CreateMap<GetProductDto, ProductEntity>().ReverseMap();
            //产品新增
            CreateMap<InsertupdateproductentityDto, ProductEntity>().ReverseMap();
            //生产计划
            CreateMap<InsertupdateproductionplanDto, ProductionPlan>().ReverseMap();
            CreateMap<GetproductionplanDto, ProductionPlan>().ReverseMap();

            //报工质检 - 基础映射，关联信息通过服务层手动处理
            CreateMap<CreateWorkReportInspectionDto, WorkReportInspectionEntity>()
                .ForMember(dest => dest.Id, opt => opt.Ignore()); // ID由服务层生成

            CreateMap<UpdateWorkReportInspectionDto, WorkReportInspectionEntity>()
                .ForAllMembers(opts => opts.Condition((src, dest, srcMember) => srcMember != null));

            // 添加缺失的映射：从实体到DTO
            CreateMap<WorkReportInspectionEntity, GetWorkReportInspectionDto>()
                .ForMember(dest => dest.QualificationRate, opt => opt.MapFrom(src =>
                    src.TestedQuantity.HasValue && src.TestedQuantity.Value > 0 && src.QualifiedQuantity.HasValue
                        ? (decimal)src.QualifiedQuantity.Value / src.TestedQuantity.Value
                        : (decimal?)null))
                // 关联字段在服务层通过联表查询设置，这里忽略
                .ForMember(dest => dest.ProductName, opt => opt.Ignore())
                .ForMember(dest => dest.ProductCode, opt => opt.Ignore())
                .ForMember(dest => dest.ProcessStepName, opt => opt.Ignore())
                .ForMember(dest => dest.StationName, opt => opt.Ignore())
                .ForMember(dest => dest.TeamName, opt => opt.Ignore())
                .ForMember(dest => dest.ReporterName, opt => opt.Ignore())
                .ForMember(dest => dest.InspectorName, opt => opt.Ignore())
                .ForMember(dest => dest.TaskName, opt => opt.Ignore());

        }
    }
}
