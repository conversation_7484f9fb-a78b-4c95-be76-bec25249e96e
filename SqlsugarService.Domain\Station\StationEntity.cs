using SqlSugar;
using SqlsugarService.Domain.Common;
using SqlsugarService.Domain.Craftsmanship;
using System;
using System.Collections.Generic;

namespace SqlsugarService.Domain.Station
{
    /// <summary>
    /// 站点实体类
    /// </summary>
    public class StationEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 站点编号
        /// </summary>
        public string StationCode { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        public string StationName { get; set; }

        /// <summary>
        /// 站点类型
        /// </summary>
        public string StationType { get; set; }

        /// <summary>
        /// 所在车间
        /// </summary>
        public string Workshop { get; set; }

        /// <summary>
        /// 生产线
        /// </summary>
        public string ProductionLine { get; set; }
        
        /// <summary>
        /// 所属工序Id
        /// </summary>
        public Guid ProcessStepId { get; set; }

        /// <summary>
        /// 工序导航属性
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(ProcessStepId))]
        public ProcessStep ProcessStep { get; set; }

        /// <summary>
        /// 站点位置
        /// </summary>
        public string StationLocation { get; set; }

        /// <summary>
        /// 状态 (如: 启用, 禁用)
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 站点描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 岗位Id (外键)
        /// </summary>
        public Guid PositionId { get; set; }

        /// <summary>
        /// 设备列表导航属性
        /// </summary>
        [Navigate(NavigateType.OneToMany, nameof(EquipmentEntity.StationId))]
        public List<EquipmentEntity> Equipments { get; set; }

        /// <summary>
        /// 工具列表导航属性
        /// </summary>
        [Navigate(NavigateType.OneToMany, nameof(ToolEntity.StationId))]
        public List<ToolEntity> Tools { get; set; }

        /// <summary>
        /// 软删除标记
        /// </summary>
        public new bool IsDeleted { get; set; } = false;
    }
} 