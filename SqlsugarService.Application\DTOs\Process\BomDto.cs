using SqlsugarService.Domain.Craftsmanship;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SqlsugarService.Application.DTOs.Process
{
    /// <summary>
    /// BOM数据传输对象 - 用于创建新的BOM
    /// </summary>
    public class BomDto
    {
        /// <summary>
        /// BOM编号（必填）
        /// </summary>
        [Required(ErrorMessage = "BOM编号不能为空")]
        [StringLength(50, ErrorMessage = "BOM编号长度不能超过50个字符")]
        public string BomNumber { get; set; } = string.Empty;

        /// <summary>
        /// 是否系统编号（默认false）
        /// </summary>
        public bool IsSystemNumber { get; set; } = false;

        /// <summary>
        /// 是否默认BOM（默认false）
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// BOM版本（必填）
        /// </summary>
        [Required(ErrorMessage = "BOM版本不能为空")]
        [StringLength(20, ErrorMessage = "BOM版本长度不能超过20个字符")]
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// 产品Id（必填）
        /// </summary>
        [Required(ErrorMessage = "产品ID不能为空")]
        [Range(1, double.MaxValue, ErrorMessage = "产品ID必须大于0")]
        public decimal ProductId { get; set; }

        /// <summary>
        /// 产品名称（必填）
        /// </summary>
        [Required(ErrorMessage = "产品名称不能为空")]
        [StringLength(200, ErrorMessage = "产品名称长度不能超过200个字符")]
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// 颜色代码/规格型号（可选）
        /// </summary>
        [StringLength(100, ErrorMessage = "规格型号长度不能超过100个字符")]
        public string? ColorCode { get; set; }

        /// <summary>
        /// 单位（可选）
        /// </summary>
        [StringLength(20, ErrorMessage = "单位长度不能超过20个字符")]
        public string? Unit { get; set; }

        /// <summary>
        /// 日产量（必填）
        /// </summary>
        [Required(ErrorMessage = "日产量不能为空")]
        [Range(0.001, double.MaxValue, ErrorMessage = "日产量必须大于0")]
        public decimal DailyOutput { get; set; }

        /// <summary>
        /// 备注（可选）
        /// </summary>
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        /// <summary>
        /// 工艺路线Id（必填）
        /// </summary>
        [Required(ErrorMessage = "工艺路线ID不能为空")]
        public Guid ProcessRouteId { get; set; }

        /// <summary>
        /// BOM明细列表（可选，创建时可以为空，后续添加）
        /// </summary>
        public List<BomItemDto>? BomItems { get; set; }
    }

    /// <summary>
    /// BOM明细数据传输对象
    /// </summary>
    public class BomItemDto
    {
        /// <summary>
        /// 产品物料Id（必填）
        /// </summary>
        [Required(ErrorMessage = "物料ID不能为空")]
        public Guid MaterialId { get; set; }

        /// <summary>
        /// 父级明细Id（可选，用于层级结构）
        /// </summary>
        public Guid? ParentItemId { get; set; }

        /// <summary>
        /// 用量（必填）
        /// </summary>
        [Required(ErrorMessage = "用量不能为空")]
        [Range(0.001, double.MaxValue, ErrorMessage = "用量必须大于0")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 损耗率（百分比，0-100）
        /// </summary>
        [Range(0, 100, ErrorMessage = "损耗率必须在0-100之间")]
        public decimal LossRate { get; set; } = 0;

        /// <summary>
        /// 投入产出类型（必填）
        /// </summary>
        [Required(ErrorMessage = "投入产出类型不能为空")]
        public MaterialRelationType InOutType { get; set; }

        /// <summary>
        /// 单位（可选）
        /// </summary>
        [StringLength(20, ErrorMessage = "单位长度不能超过20个字符")]
        public string? Unit { get; set; }

        /// <summary>
        /// 备注（可选）
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remark { get; set; }
    }

    /// <summary>
    /// BOM列表显示DTO
    /// </summary>
    public class BomListDto
    {
        /// <summary>
        /// BOM ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// BOM编号
        /// </summary>
        public string BomNumber { get; set; } = string.Empty;

        /// <summary>
        /// 是否系统编号
        /// </summary>
        public bool IsSystemNumber { get; set; }

        /// <summary>
        /// 是否默认BOM
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// BOM版本
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// 产品Id
        /// </summary>
        public decimal ProductId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// 颜色代码/规格型号
        /// </summary>
        public string? ColorCode { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 日产量
        /// </summary>
        public decimal DailyOutput { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 工艺路线Id
        /// </summary>
        public Guid ProcessRouteId { get; set; }

        /// <summary>
        /// 工艺路线名称
        /// </summary>
        public string ProcessRouteName { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdatedTime { get; set; }

        /// <summary>
        /// BOM明细数量
        /// </summary>
        public int ItemCount { get; set; }
    }

    /// <summary>
    /// BOM明细列表显示DTO
    /// </summary>
    public class BomItemListDto
    {
        /// <summary>
        /// 明细ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// BOM主表Id
        /// </summary>
        public Guid BomId { get; set; }

        /// <summary>
        /// 产品物料Id
        /// </summary>
        public Guid MaterialId { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        public string MaterialNumber { get; set; } = string.Empty;

        /// <summary>
        /// 物料名称
        /// </summary>
        public string MaterialName { get; set; } = string.Empty;

        /// <summary>
        /// 父级明细Id
        /// </summary>
        public Guid? ParentItemId { get; set; }

        /// <summary>
        /// 用量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 损耗率
        /// </summary>
        public decimal LossRate { get; set; }

        /// <summary>
        /// 投入产出类型
        /// </summary>
        public MaterialRelationType InOutType { get; set; }

        /// <summary>
        /// 投入产出类型名称
        /// </summary>
        public string InOutTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }
}