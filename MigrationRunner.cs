using System;
using System.Threading.Tasks;
using Npgsql;

namespace DatabaseMigration
{
    class MigrationRunner
    {
        private const string ConnectionString = "Host=*************;Port=5432;Database=sqlsugardata;Username=****;Password=****;SearchPath=public";

        static async Task Main(string[] args)
        {
            Console.WriteLine("开始执行数据库迁移...");
            Console.WriteLine();

            try
            {
                await RunMigrationAsync();
                Console.WriteLine("迁移执行成功！");
                Console.WriteLine("现在可以重新启动API服务测试质检功能");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"迁移执行失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        static async Task RunMigrationAsync()
        {
            using var connection = new NpgsqlConnection(ConnectionString);
            await connection.OpenAsync();

            Console.WriteLine("数据库连接成功");

            // 检查表是否存在
            var checkTableSql = @"
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'workreportinspectionentity'
                );";

            using var checkCmd = new NpgsqlCommand(checkTableSql, connection);
            var tableExists = (bool)await checkCmd.ExecuteScalarAsync();

            if (!tableExists)
            {
                Console.WriteLine("警告: 质检表不存在，可能需要先创建表结构");
                return;
            }

            Console.WriteLine("质检表存在，继续执行迁移...");

            // 检查字段是否已存在
            var checkColumnsSql = @"
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'workreportinspectionentity' 
                  AND column_name IN ('productionplanid', 'productionorderid');";

            using var checkColumnsCmd = new NpgsqlCommand(checkColumnsSql, connection);
            using var reader = await checkColumnsCmd.ExecuteReaderAsync();
            
            var existingColumns = new List<string>();
            while (await reader.ReadAsync())
            {
                existingColumns.Add(reader.GetString(0));
            }
            reader.Close();

            // 执行迁移SQL
            var migrationSqls = new[]
            {
                "ALTER TABLE workreportinspectionentity ADD COLUMN IF NOT EXISTS productionplanid UUID NULL;",
                "ALTER TABLE workreportinspectionentity ADD COLUMN IF NOT EXISTS productionorderid UUID NULL;",
                "COMMENT ON COLUMN workreportinspectionentity.productionplanid IS '生产计划Id';",
                "COMMENT ON COLUMN workreportinspectionentity.productionorderid IS '工单Id (生产工单)';"
            };

            foreach (var sql in migrationSqls)
            {
                using var cmd = new NpgsqlCommand(sql, connection);
                await cmd.ExecuteNonQueryAsync();
                Console.WriteLine($"执行SQL: {sql.Substring(0, Math.Min(50, sql.Length))}...");
            }

            // 验证迁移结果
            var verifySql = @"
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'workreportinspectionentity' 
                  AND column_name IN ('productionplanid', 'productionorderid')
                ORDER BY column_name;";

            using var verifyCmd = new NpgsqlCommand(verifySql, connection);
            using var verifyReader = await verifyCmd.ExecuteReaderAsync();

            Console.WriteLine();
            Console.WriteLine("验证结果:");
            Console.WriteLine("字段名\t\t数据类型\t可空");
            Console.WriteLine("----------------------------------------");

            while (await verifyReader.ReadAsync())
            {
                var columnName = verifyReader.GetString(0);
                var dataType = verifyReader.GetString(1);
                var isNullable = verifyReader.GetString(2);
                Console.WriteLine($"{columnName}\t{dataType}\t\t{isNullable}");
            }
        }
    }
}