﻿using SqlsugarService.Application.DTOs.PlanDto;
using SqlsugarService.Application.Until;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.IService.Plan
{
    public interface IProductEntityService
    {
        Task<ApiResult> UpdateProduct(InsertupdateproductentityDto dto);
        Task<ApiResult> AddProduct(InsertupdateproductentityDto dto);
        Task<ApiResult<PageResult<List<GetProductDto>>>> GetProductList(GetProductSearchDto search);
        Task<ApiResult> DeleteProduct(Guid id);

    }
}
