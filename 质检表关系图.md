# 质检表关系图

## 🗂️ 数据库表关系图

```
                    质检数据来源表关系图

    ┌─────────────────────────────────────────────────────────────┐
    │                   报工质检表 (主表)                          │
    │              WorkReportInspectionEntity                     │
    │  ┌─────────────────────────────────────────────────────┐    │
    │  │ • Id (主键)                                         │    │
    │  │ • InspectionCode (检验单号)                         │    │
    │  │ • InspectionName (检验单名称)                       │    │
    │  │ • InspectionType (检验类型)                         │    │
    │  │ • Status (状态)                                     │    │
    │  │ • ReportedQuantity (报工数量)                       │    │
    │  │ • ReportTime (报工时间)                             │    │
    │  │ • TestedQuantity (检测数量)                         │    │
    │  │ • QualifiedQuantity (合格数量)                      │    │
    │  │ • UnqualifiedQuantity (不合格数量)                  │    │
    │  │ • InspectionTime (检验时间)                         │    │
    │  │ • OverallResult (检测结果)                          │    │
    │  └─────────────────────────────────────────────────────┘    │
    └─────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │               │               │
                    ▼               ▼               ▼

    ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
    │   产品表 ✅      │  │   工序表 ✅      │  │   站点表 ✅      │
    │  ProductEntity  │  │  ProcessStep    │  │ StationEntity   │
    │ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌─────────────┐ │
    │ │MaterialName │ │  │ │ProcessStep  │ │  │ │StationName  │ │
    │ │MaterialNum  │ │  │ │Name         │ │  │ │             │ │
    │ └─────────────┘ │  │ └─────────────┘ │  │ └─────────────┘ │
    └─────────────────┘  └─────────────────┘  └─────────────────┘
            │                       │                       │
            │ProductId              │ProcessStepId          │StationId
            │                       │                       │
            └───────────────────────┼───────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │               │               │
                    ▼               ▼               ▼

    ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
    │   班组表 ✅      │  │  用户表 ✅       │  │  用户表 ✅       │
    │  TeamEntity     │  │  Users          │  │  Users          │
    │ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌─────────────┐ │
    │ │TeamName     │ │  │ │DisplayName  │ │  │ │DisplayName  │ │
    │ │             │ │  │ │(报工人员)    │ │  │ │(检验人员)    │ │
    │ └─────────────┘ │  │ └─────────────┘ │  │ └─────────────┘ │
    └─────────────────┘  └─────────────────┘  └─────────────────┘
            │                       │                       │
            │TeamId                 │ReporterId             │InspectorId
            │                       │                       │
            └───────────────────────┼───────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │               │               │
                    ▼               ▼               ▼

    ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
    │ 工单任务表 ⚠️    │  │ 生产计划表 ⚠️    │  │ 生产工单表 ⚠️    │
    │WorkOrderTask    │  │ProductionPlan   │  │ProductionOrder  │
    │Entity           │  │                 │  │                 │
    │ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌─────────────┐ │
    │ │TaskName     │ │  │ │PlanName     │ │  │ │OrderName    │ │
    │ │TaskNumber   │ │  │ │PlanNumber   │ │  │ │OrderCode    │ │
    │ └─────────────┘ │  │ └─────────────┘ │  │ └─────────────┘ │
    └─────────────────┘  └─────────────────┘  └─────────────────┘
            │                       │                       │
            │TaskId                 │ProductionPlanId       │ProductionOrderId
            │⚠️ 暂时忽略             │⚠️ 暂时忽略             │⚠️ 暂时忽略
            │                       │                       │
            └───────────────────────┼───────────────────────┘
                                    │
                                    ▼
                            [数据库迁移后可用]
```

## 📊 表关系详细说明

### ✅ 当前可用的关联 (6 个表)

| 序号 | 关联表        | 外键字段      | 获取数据           | 状态    |
| ---- | ------------- | ------------- | ------------------ | ------- |
| 1    | ProductEntity | ProductId     | 产品名称、产品编号 | ✅ 正常 |
| 2    | ProcessStep   | ProcessStepId | 工序名称           | ✅ 正常 |
| 3    | StationEntity | StationId     | 站点名称           | ✅ 正常 |
| 4    | TeamEntity    | TeamId        | 班组名称           | ✅ 正常 |
| 5    | Users (报工)  | ReporterId    | 报工人员姓名       | ✅ 正常 |
| 6    | Users (检验)  | InspectorId   | 检验人员姓名       | ✅ 正常 |

### ⚠️ 暂时不可用的关联 (3 个表)

| 序号 | 关联表              | 外键字段          | 获取数据           | 状态        |
| ---- | ------------------- | ----------------- | ------------------ | ----------- |
| 7    | WorkOrderTaskEntity | TaskId            | 任务名称、任务编号 | ⚠️ 字段缺失 |
| 8    | ProductionPlan      | ProductionPlanId  | 计划名称、计划编号 | ⚠️ 字段缺失 |
| 9    | ProductionOrder     | ProductionOrderId | 工单名称、工单编号 | ⚠️ 字段缺失 |

## 🔍 数据查询示例

### 当前可用的查询

```sql
SELECT
    -- 主表数据
    wi.Id,
    wi.InspectionCode,
    wi.InspectionName,
    wi.Status,
    wi.ReportedQuantity,
    wi.ReportTime,

    -- 关联表数据 ✅
    p.MaterialName as ProductName,
    p.MaterialNumber as ProductCode,
    ps.ProcessStepName,
    s.StationName,
    t.TeamName,
    r.DisplayName as ReporterName,
    i.DisplayName as InspectorName

FROM workreportinspectionentity wi
LEFT JOIN productentity p ON wi.ProductId = p.Id
LEFT JOIN processstep ps ON wi.ProcessStepId = ps.Id
LEFT JOIN stationentity s ON wi.StationId = s.Id
LEFT JOIN teamentity t ON wi.TeamId = t.Id
LEFT JOIN users r ON wi.ReporterId = r.Id
LEFT JOIN users i ON wi.InspectorId = i.Id
-- LEFT JOIN workordertaskentity wt ON wi.TaskId = wt.Id ⚠️ 暂时不可用
-- LEFT JOIN productionplan pp ON wi.ProductionPlanId = pp.Id ⚠️ 暂时不可用
-- LEFT JOIN productionorder po ON wi.ProductionOrderId = po.Id ⚠️ 暂时不可用
```

### 完整功能后的查询

```sql
SELECT
    -- 主表数据
    wi.*,

    -- 当前可用的关联数据 ✅
    p.MaterialName as ProductName,
    p.MaterialNumber as ProductCode,
    ps.ProcessStepName,
    s.StationName,
    t.TeamName,
    r.DisplayName as ReporterName,
    i.DisplayName as InspectorName,

    -- 迁移后可用的关联数据 🔄
    wt.TaskName,
    wt.TaskNumber as TaskCode,
    pp.PlanName,
    pp.PlanNumber,
    po.OrderName as WorkOrderName,
    po.OrderCode as WorkOrderCode

FROM workreportinspectionentity wi
LEFT JOIN productentity p ON wi.ProductId = p.Id
LEFT JOIN processstep ps ON wi.ProcessStepId = ps.Id
LEFT JOIN stationentity s ON wi.StationId = s.Id
LEFT JOIN teamentity t ON wi.TeamId = t.Id
LEFT JOIN users r ON wi.ReporterId = r.Id
LEFT JOIN users i ON wi.InspectorId = i.Id
LEFT JOIN workordertaskentity wt ON wi.TaskId = wt.Id
LEFT JOIN productionplan pp ON wi.ProductionPlanId = pp.Id
LEFT JOIN productionorder po ON wi.ProductionOrderId = po.Id
```

## 🎯 业务流程中的表关系

### 1. 生产流程

```
生产计划表 → 生产工单表 → 工单任务表 → 报工质检表
    ↓           ↓           ↓           ↓
  计划信息    工单信息    任务信息    质检记录
```

### 2. 组织结构

```
用户表 → 班组表 → 站点表 → 工序表
  ↓       ↓       ↓       ↓
人员信息  班组信息  站点信息  工序信息
```

### 3. 产品信息

```
产品表 → 报工质检表
  ↓         ↓
产品信息   质检记录
```

## 📝 数据完整性说明

### 必需关联 (不能为空)

- ✅ ProductId - 产品信息
- ✅ ProcessStepId - 工序信息
- ✅ StationId - 站点信息
- ✅ TeamId - 班组信息
- ✅ ReporterId - 报工人员

### 可选关联 (可以为空)

- ✅ InspectorId - 检验人员 (质检前可能为空)
- ⚠️ TaskId - 任务信息 (某些质检可能不关联具体任务)
- ⚠️ ProductionPlanId - 计划信息 (临时质检可能不关联计划)
- ⚠️ ProductionOrderId - 工单信息 (某些质检可能不关联工单)

---

**图例说明**:

- ✅ 表示当前完全可用的关联
- ⚠️ 表示需要数据库迁移后才能使用的关联
- 🔄 表示迁移后恢复的功能
