using SqlSugar;
using SqlsugarService.Domain.Common;
using System;

namespace SqlsugarService.Domain.Station
{
    /// <summary>
    /// 设备信息实体类 (设备台账)
    /// </summary>
    public class EquipmentEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 设备名称
        /// </summary>
        public string EquipmentName { get; set; }

        /// <summary>
        /// 设备编号
        /// </summary>
        public string EquipmentCode { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string SpecificationModel { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        public string Brand { get; set; }

        /// <summary>
        /// 所属车间
        /// </summary>
        public string Workshop { get; set; }

        /// <summary>
        /// 设备分类
        /// </summary>
        public string EquipmentCategory { get; set; }

        /// <summary>
        /// 设备状态 (如: 启用, 停用, 维修中)
        /// </summary>
        public string Status { get; set; }
        
        /// <summary>
        /// 站点Id (外键)
        /// </summary>
        public Guid StationId { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public Guid CreatorId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
} 