using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SqlsugarService.Application.DTOs.Process;
using SqlsugarService.Application.IService.Process.ProcessComposition;
using SqlsugarService.Application.Until;
using SqlsugarService.Domain.Craftsmanship;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SqlsugarService.API.Controllers.BomRelatedController
{
    /// <summary>
    /// 工序相关控制器
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class ProcessController : ControllerBase
    {
        // 私有字段：依赖注入的工序组成服务
        private readonly IProcessCompositionService _processCompositionService;

        /// <summary>
        /// 构造函数：通过依赖注入获取工序组成服务
        /// </summary>
        /// <param name="processCompositionService">工序组成服务接口</param>
        public ProcessController(IProcessCompositionService processCompositionService)
        {
            _processCompositionService = processCompositionService ?? throw new ArgumentNullException(nameof(processCompositionService));
        }

        #region 工序组成管理

        /// <summary>
        /// 添加工序组成
        /// 功能：将指定的工序添加到工艺路线中，建立工序组成关系
        /// </summary>
        /// <param name="dto">工序组成信息，包含工艺路线ID、工序ID、顺序号等</param>
        /// <returns>操作结果，成功返回200，失败返回错误信息</returns>
        [HttpPost]
        public async Task<ApiResult> AddProcessComposition([FromBody] ProcessCompositionDto dto)
        {
            try
            {
                // 参数验证：检查DTO是否为空
                if (dto == null)
                {
                    return ApiResult.Fail("请求参数不能为空", ResultCode.ValidationError);
                }

                // 调用业务服务层处理工序组成添加逻辑
                return await _processCompositionService.AddProcessCompositionAsync(dto);
            }
            catch (Exception ex)
            {
                // 捕获并处理所有未预期的异常，返回统一的错误格式
                return ApiResult.Fail($"添加工序组成失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 分页获取工序组成列表
        /// 功能：根据条件查询工序组成列表，支持分页显示和工艺路线过滤
        /// </summary>
        /// <param name="processRouteId">工艺路线ID（可选），为空时获取所有工序组成</param>
        /// <param name="pageIndex">页码，从1开始，默认为1</param>
        /// <param name="pageSize">每页数量，默认为10，最大不超过1000</param>
        /// <returns>分页的工序组成列表，包含工艺路线和工序的详细信息</returns>
        [HttpGet]
        public async Task<ApiResult<PageResult<List<ProcessCompositionListDto>>>> GetProcessCompositionPaged(
            [FromQuery] Guid? processRouteId,
            [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                // 参数验证：检查分页参数的合理性
                if (pageIndex < 1)
                {
                    return ApiResult<PageResult<List<ProcessCompositionListDto>>>.Fail(
                        "页码必须大于0", ResultCode.ValidationError);
                }

                if (pageSize < 1 || pageSize > 1000)
                {
                    return ApiResult<PageResult<List<ProcessCompositionListDto>>>.Fail(
                        "每页大小必须在1-1000之间", ResultCode.ValidationError);
                }

                // 调用业务服务层处理分页查询逻辑
                return await _processCompositionService.GetProcessCompositionPagedAsync(processRouteId, pageIndex, pageSize);
            }
            catch (Exception ex)
            {
                // 捕获并处理所有未预期的异常，返回统一的错误格式
                return ApiResult<PageResult<List<ProcessCompositionListDto>>>.Fail(
                    $"获取工序组成列表失败: {ex.Message}", 
                    ResultCode.Error);
            }
        }

        #endregion

        #region 工序物料详情管理

        /// <summary>
        /// 单个添加工序物料详情
        /// 功能：为指定工序添加一个物料详情记录
        /// </summary>
        /// <param name="dto">工序物料详情信息</param>
        /// <returns>操作结果，成功返回200，失败返回错误信息</returns>
        [HttpPost]
        public async Task<ApiResult> AddProcessStepMaterialDetail([FromBody] ProcessStepMaterialDetailDto dto)
        {
            try
            {
                // 参数验证：检查DTO是否为空
                if (dto == null)
                {
                    return ApiResult.Fail("请求参数不能为空", ResultCode.ValidationError);
                }

                // 调用业务服务层处理工序物料详情添加逻辑
                return await _processCompositionService.AddProcessStepMaterialDetailAsync(dto);
            }
            catch (Exception ex)
            {
                // 捕获并处理所有未预期的异常，返回统一的错误格式
                return ApiResult.Fail($"添加工序物料详情失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 批量添加工序物料详情
        /// 功能：为指定工序批量添加多个物料详情记录
        /// </summary>
        /// <param name="batchDto">批量工序物料详情信息</param>
        /// <returns>操作结果，成功返回200，失败返回错误信息</returns>
        [HttpPost]
        public async Task<ApiResult> BatchAddProcessStepMaterialDetails([FromBody] BatchProcessStepMaterialDetailDto batchDto)
        {
            try
            {
                // 参数验证：检查DTO是否为空
                if (batchDto == null)
                {
                    return ApiResult.Fail("请求参数不能为空", ResultCode.ValidationError);
                }

                // 调用业务服务层处理批量添加逻辑
                return await _processCompositionService.BatchAddProcessStepMaterialDetailsAsync(batchDto);
            }
            catch (Exception ex)
            {
                // 捕获并处理所有未预期的异常，返回统一的错误格式
                return ApiResult.Fail($"批量添加工序物料详情失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 分页获取工序物料详情列表
        /// 功能：根据条件查询工序物料详情列表，支持分页显示和多种过滤条件
        /// </summary>
        /// <param name="processStepId">工序ID（可选），为空时获取所有工序的物料详情</param>
        /// <param name="ioType">投入产出类型（可选），为空时获取所有类型</param>
        /// <param name="pageIndex">页码，从1开始，默认为1</param>
        /// <param name="pageSize">每页数量，默认为10，最大不超过1000</param>
        /// <returns>分页的工序物料详情列表</returns>
        [HttpGet]
        public async Task<ApiResult<PageResult<List<ProcessStepMaterialDetailListDto>>>> GetProcessStepMaterialDetailsPaged(
            [FromQuery] Guid? processStepId,
            [FromQuery] MaterialIOType? ioType,
            [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                // 参数验证：检查分页参数的合理性
                if (pageIndex < 1)
                {
                    return ApiResult<PageResult<List<ProcessStepMaterialDetailListDto>>>.Fail(
                        "页码必须大于0", ResultCode.ValidationError);
                }

                if (pageSize < 1 || pageSize > 1000)
                {
                    return ApiResult<PageResult<List<ProcessStepMaterialDetailListDto>>>.Fail(
                        "每页大小必须在1-1000之间", ResultCode.ValidationError);
                }

                // 调用业务服务层处理分页查询逻辑
                return await _processCompositionService.GetProcessStepMaterialDetailsPagedAsync(processStepId, ioType, pageIndex, pageSize);
            }
            catch (Exception ex)
            {
                // 捕获并处理所有未预期的异常，返回统一的错误格式
                return ApiResult<PageResult<List<ProcessStepMaterialDetailListDto>>>.Fail(
                    $"获取工序物料详情列表失败: {ex.Message}", 
                    ResultCode.Error);
            }
        }

        #endregion

        #region 工艺路线管理

        /// <summary>
        /// 新增工艺路线
        /// 功能：创建新的工艺路线，作为工序组成的基础
        /// 路由：POST /api/Process/AddProcessRoute
        /// </summary>
        /// <param name="dto">工艺路线信息</param>
        /// <returns>操作结果，成功返回200，失败返回错误信息</returns>
        [HttpPost]
        public async Task<ApiResult> AddProcessRoute([FromBody] ProcessRouteDto dto)
        {
            try
            {
                // 参数验证：检查DTO是否为空
                if (dto == null)
                {
                    return ApiResult.Fail("请求参数不能为空", ResultCode.ValidationError);
                }

                // 调用业务服务层处理工艺路线添加逻辑
                return await _processCompositionService.AddProcessRouteAsync(dto);
            }
            catch (Exception ex)
            {
                // 捕获并处理所有未预期的异常，返回统一的错误格式
                return ApiResult.Fail($"添加工艺路线失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 分页获取工艺路线列表
        /// 功能：根据条件查询工艺路线列表，支持分页显示和状态过滤
        /// 路由：GET /api/Process/GetProcessRoutePaged
        /// </summary>
        /// <param name="status">状态过滤（可选），如"启用"或"禁用"</param>
        /// <param name="pageIndex">页码，从1开始，默认为1</param>
        /// <param name="pageSize">每页数量，默认为10，最大不超过1000</param>
        /// <returns>分页的工艺路线列表</returns>
        [HttpGet]
        public async Task<ApiResult<PageResult<List<ProcessRouteListDto>>>> GetProcessRoutePaged(
            [FromQuery] string? status,
            [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                // 参数验证：检查分页参数的合理性
                if (pageIndex < 1)
                {
                    return ApiResult<PageResult<List<ProcessRouteListDto>>>.Fail(
                        "页码必须大于0", ResultCode.ValidationError);
                }

                if (pageSize < 1 || pageSize > 1000)
                {
                    return ApiResult<PageResult<List<ProcessRouteListDto>>>.Fail(
                        "每页大小必须在1-1000之间", ResultCode.ValidationError);
                }

                // 调用业务服务层处理分页查询逻辑
                return await _processCompositionService.GetProcessRoutePagedAsync(status, pageIndex, pageSize);
            }
            catch (Exception ex)
            {
                // 捕获并处理所有未预期的异常，返回统一的错误格式
                return ApiResult<PageResult<List<ProcessRouteListDto>>>.Fail(
                    $"获取工艺路线列表失败: {ex.Message}", 
                    ResultCode.Error);
            }
        }

        #endregion

        #region 工序管理

        /// <summary>
        /// 新增工序
        /// 功能：创建新的工序，作为工序组成的基础
        /// 路由：POST /api/Process/AddProcessStep
        /// </summary>
        /// <param name="dto">工序信息</param>
        /// <returns>操作结果，成功返回200，失败返回错误信息</returns>
        [HttpPost]
        public async Task<ApiResult> AddProcessStep([FromBody] ProcessStepDto dto)
        {
            try
            {
                // 参数验证：检查DTO是否为空
                if (dto == null)
                {
                    return ApiResult.Fail("请求参数不能为空", ResultCode.ValidationError);
                }

                // 调用业务服务层处理工序添加逻辑
                return await _processCompositionService.AddProcessStepAsync(dto);
            }
            catch (Exception ex)
            {
                // 捕获并处理所有未预期的异常，返回统一的错误格式
                return ApiResult.Fail($"添加工序失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 分页获取工序列表
        /// 功能：根据条件查询工序列表，支持分页显示和状态过滤
        /// 路由：GET /api/Process/GetProcessStepPaged
        /// </summary>
        /// <param name="status">状态过滤（可选），0=禁用，1=启用</param>
        /// <param name="pageIndex">页码，从1开始，默认为1</param>
        /// <param name="pageSize">每页数量，默认为10，最大不超过1000</param>
        /// <returns>分页的工序列表</returns>
        [HttpGet]
        public async Task<ApiResult<PageResult<List<ProcessStepListDto>>>> GetProcessStepPaged(
            [FromQuery] StepStatus? status,
            [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                // 参数验证：检查分页参数的合理性
                if (pageIndex < 1)
                {
                    return ApiResult<PageResult<List<ProcessStepListDto>>>.Fail(
                        "页码必须大于0", ResultCode.ValidationError);
                }

                if (pageSize < 1 || pageSize > 1000)
                {
                    return ApiResult<PageResult<List<ProcessStepListDto>>>.Fail(
                        "每页大小必须在1-1000之间", ResultCode.ValidationError);
                }

                // 调用业务服务层处理分页查询逻辑
                return await _processCompositionService.GetProcessStepPagedAsync(status, pageIndex, pageSize);
            }
            catch (Exception ex)
            {
                // 捕获并处理所有未预期的异常，返回统一的错误格式
                return ApiResult<PageResult<List<ProcessStepListDto>>>.Fail(
                    $"获取工序列表失败: {ex.Message}", 
                    ResultCode.Error);
            }
        }

        #endregion
    }
}
