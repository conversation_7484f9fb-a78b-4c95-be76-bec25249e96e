using AutoMapper;
using SqlsugarService.Application.DTOs.WorkReportInspectionDto;
using SqlsugarService.Application.IService.WorkReportInspection;
using SqlsugarService.Application.Until;
using SqlsugarService.Domain.QualityInspection;
using SqlsugarService.Infrastructure.IRepository;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.Service.WorkReportInspection
{
    /// <summary>
    /// 报工质检服务实现
    /// </summary>
    public class WorkReportInspectionService : IWorkReportInspectionService
    {
        private readonly IBaseRepository<WorkReportInspectionEntity> _workReportInspectionRepository;
        private readonly IMapper _mapper;

        public WorkReportInspectionService(
            IBaseRepository<WorkReportInspectionEntity> workReportInspectionRepository,
            IMapper mapper)
        {
            _workReportInspectionRepository = workReportInspectionRepository;
            _mapper = mapper;
        }

        /// <summary>
        /// 获取质检记录列表
        /// </summary>
        /// <param name="searchDto">搜索条件</param>
        /// <returns>质检记录列表</returns>
        public async Task<ApiResult<PageResult<List<GetWorkReportInspectionDto>>>> GetListAsync(GetWorkReportInspectionSearchDto searchDto)
        {
            try
            {
                // 先获取基础质检记录，不强制要求关联数据存在
                var query = _workReportInspectionRepository.AsQueryable();

                // 只包含存在数据的关联表
                query = query.Includes(x => x.Product); // 产品数据存在，可以包含

                // 应用搜索条件 - 支持前端界面的搜索字段

                // 计划编号搜索 (关联生产计划表)
                if (!string.IsNullOrEmpty(searchDto.PlanNumber?.Trim()))
                {
                    // 这里需要关联生产计划表进行搜索
                    // 暂时通过产品相关字段进行模糊搜索
                    query = query.Where(x => x.Product != null &&
                        (x.Product.MaterialNumber.Contains(searchDto.PlanNumber.Trim()) ||
                         x.Product.MaterialName.Contains(searchDto.PlanNumber.Trim())));
                }

                // 产品名称搜索
                if (!string.IsNullOrEmpty(searchDto.ProductName?.Trim()))
                {
                    query = query.Where(x => x.Product != null &&
                        x.Product.MaterialName.Contains(searchDto.ProductName.Trim()));
                }

                // 状态搜索
                if (!string.IsNullOrEmpty(searchDto.Status?.Trim()))
                {
                    query = query.Where(x => x.Status.Contains(searchDto.Status.Trim()));
                }

                // 保留原有搜索条件以兼容现有功能
                if (!string.IsNullOrEmpty(searchDto.InspectionCode?.Trim()))
                {
                    query = query.Where(x => x.InspectionCode.Contains(searchDto.InspectionCode.Trim()));
                }

                if (!string.IsNullOrEmpty(searchDto.InspectionName?.Trim()))
                {
                    query = query.Where(x => x.InspectionName.Contains(searchDto.InspectionName.Trim()));
                }

                if (!string.IsNullOrEmpty(searchDto.InspectionType?.Trim()))
                {
                    query = query.Where(x => x.InspectionType == searchDto.InspectionType.Trim());
                }

                if (!string.IsNullOrEmpty(searchDto.Status?.Trim()))
                {
                    query = query.Where(x => x.Status == searchDto.Status.Trim());
                }

                // 分页查询
                var totalCount = await query.CountAsync();
                var entities = await query
                    .OrderByDescending(x => x.Id)
                    .Skip((searchDto.PageIndex - 1) * searchDto.PageSize)
                    .Take(searchDto.PageSize)
                    .ToListAsync();

                // 转换为DTO并填充关联信息
                var dtos = entities.Select(entity => new GetWorkReportInspectionDto
                {
                    Id = entity.Id,
                    InspectionCode = entity.InspectionCode,
                    InspectionName = entity.InspectionName,
                    InspectionType = entity.InspectionType,
                    Status = entity.Status,

                    // 外键
                    ProductId = entity.ProductId,
                    ProcessStepId = entity.ProcessStepId,
                    StationId = entity.StationId,
                    TeamId = entity.TeamId,
                    ReporterId = entity.ReporterId,
                    InspectorId = entity.InspectorId,
                    WorkOrderId = entity.WorkOrderId,
                    TaskId = entity.TaskId,

                    // 通过导航属性获取关联信息
                    ProductName = entity.Product?.MaterialName,
                    ProductCode = entity.Product?.MaterialNumber,

                    // 计划相关信息 (暂时使用产品信息，后续可关联生产计划表)
                    PlanNumber = entity.Product?.MaterialNumber, // 临时使用产品编号
                    PlanName = entity.Product?.MaterialName,     // 临时使用产品名称

                    ProcessStepName = entity.ProcessStep?.ProcessStepName,
                    StationName = entity.Station?.StationName,
                    TeamName = entity.Team?.TeamName,
                    ReporterName = entity.Reporter?.DisplayName ?? entity.Reporter?.Username,
                    InspectorName = entity.Inspector?.DisplayName ?? entity.Inspector?.Username,
                    WorkOrderName = entity.WorkOrder?.OrderName,
                    WorkOrderCode = entity.WorkOrder?.OrderNumber,
                    TaskName = entity.Task?.TaskName,
                    TaskCode = entity.Task?.TaskNumber,

                    // 报工和质检信息
                    ReportedQuantity = entity.ReportedQuantity,
                    ReportTime = entity.ReportTime,
                    InspectionTime = entity.InspectionTime,
                    InspectionDepartment = entity.InspectionDepartment,
                    TestedQuantity = entity.TestedQuantity,
                    QualifiedQuantity = entity.QualifiedQuantity,
                    UnqualifiedQuantity = entity.UnqualifiedQuantity,
                    OverallResult = entity.OverallResult,
                    Remark = entity.Remark
                }).ToList();

                var pageResult = new PageResult<List<GetWorkReportInspectionDto>>
                {
                    Data = dtos,
                    TotalCount = totalCount,
                    TotalPage = (int)Math.Ceiling((double)totalCount / searchDto.PageSize)
                };

                return ApiResult<PageResult<List<GetWorkReportInspectionDto>>>.Success(pageResult, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<PageResult<List<GetWorkReportInspectionDto>>>.Fail($"获取质检记录列表失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 根据Id获取质检记录详情
        /// </summary>
        /// <param name="id">质检记录Id</param>
        /// <returns>质检记录详情</returns>
        public async Task<ApiResult<GetWorkReportInspectionDto>> GetByIdAsync(Guid id)
        {
            try
            {
                // 使用联表查询获取完整信息
                var entity = await _workReportInspectionRepository.AsQueryable()
                    .Includes(x => x.Product)
                    .Includes(x => x.ProcessStep)
                    .Includes(x => x.Station)
                    .Includes(x => x.Team)
                    .Includes(x => x.Reporter)
                    .Includes(x => x.Inspector)
                    .Includes(x => x.WorkOrder)
                    .Includes(x => x.Task)
                    .FirstAsync(x => x.Id == id);

                if (entity == null)
                {
                    return ApiResult<GetWorkReportInspectionDto>.Fail("质检记录不存在", ResultCode.NotFound);
                }

                // 手动构建DTO，包含关联信息
                var dto = new GetWorkReportInspectionDto
                {
                    Id = entity.Id,
                    InspectionCode = entity.InspectionCode,
                    InspectionName = entity.InspectionName,
                    InspectionType = entity.InspectionType,
                    Status = entity.Status,

                    // 外键
                    ProductId = entity.ProductId,
                    ProcessStepId = entity.ProcessStepId,
                    StationId = entity.StationId,
                    TeamId = entity.TeamId,
                    ReporterId = entity.ReporterId,
                    InspectorId = entity.InspectorId,
                    WorkOrderId = entity.WorkOrderId,
                    TaskId = entity.TaskId,

                    // 通过导航属性获取关联信息
                    ProductName = entity.Product?.MaterialName,
                    ProductCode = entity.Product?.MaterialNumber,
                    ProcessStepName = entity.ProcessStep?.ProcessStepName,
                    StationName = entity.Station?.StationName,
                    TeamName = entity.Team?.TeamName,
                    ReporterName = entity.Reporter?.DisplayName ?? entity.Reporter?.Username,
                    InspectorName = entity.Inspector?.DisplayName ?? entity.Inspector?.Username,
                    WorkOrderName = entity.WorkOrder?.OrderName,
                    WorkOrderCode = entity.WorkOrder?.OrderNumber,
                    TaskName = entity.Task?.TaskName,
                    TaskCode = entity.Task?.TaskNumber,

                    // 报工和质检信息
                    ReportedQuantity = entity.ReportedQuantity,
                    ReportTime = entity.ReportTime,
                    InspectionTime = entity.InspectionTime,
                    InspectionDepartment = entity.InspectionDepartment,
                    TestedQuantity = entity.TestedQuantity,
                    QualifiedQuantity = entity.QualifiedQuantity,
                    UnqualifiedQuantity = entity.UnqualifiedQuantity,
                    OverallResult = entity.OverallResult,
                    Remark = entity.Remark
                };

                return ApiResult<GetWorkReportInspectionDto>.Success(dto, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<GetWorkReportInspectionDto>.Fail($"获取质检记录详情失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 获取质检记录列表 (高级搜索，支持关联表字段搜索)
        /// </summary>
        /// <param name="searchDto">高级搜索条件</param>
        /// <returns>质检记录列表</returns>
        public async Task<ApiResult<PageResult<List<GetWorkReportInspectionDto>>>> GetAdvancedListAsync(WorkReportInspectionAdvancedSearchDto searchDto)
        {
            try
            {
                // 使用联表查询获取完整信息
                var query = _workReportInspectionRepository.AsQueryable()
                    .Includes(x => x.Product)
                    .Includes(x => x.ProcessStep)
                    .Includes(x => x.Station)
                    .Includes(x => x.Team)
                    .Includes(x => x.Reporter)
                    .Includes(x => x.Inspector)
                    .Includes(x => x.WorkOrder)
                    .Includes(x => x.Task);

                // 基础搜索条件
                if (!string.IsNullOrWhiteSpace(searchDto.InspectionCode))
                {
                    query = query.Where(x => x.InspectionCode.Contains(searchDto.InspectionCode));
                }

                if (!string.IsNullOrWhiteSpace(searchDto.InspectionName))
                {
                    query = query.Where(x => x.InspectionName.Contains(searchDto.InspectionName));
                }

                if (!string.IsNullOrWhiteSpace(searchDto.InspectionType))
                {
                    query = query.Where(x => x.InspectionType == searchDto.InspectionType);
                }

                if (!string.IsNullOrWhiteSpace(searchDto.Status))
                {
                    query = query.Where(x => x.Status == searchDto.Status);
                }

                // 关联表搜索条件
                if (!string.IsNullOrWhiteSpace(searchDto.ProductName))
                {
                    query = query.Where(x => x.Product.MaterialName.Contains(searchDto.ProductName));
                }

                if (!string.IsNullOrWhiteSpace(searchDto.ProductCode))
                {
                    query = query.Where(x => x.Product.MaterialNumber.Contains(searchDto.ProductCode));
                }

                if (!string.IsNullOrWhiteSpace(searchDto.ProcessStepName))
                {
                    query = query.Where(x => x.ProcessStep.ProcessStepName.Contains(searchDto.ProcessStepName));
                }

                if (!string.IsNullOrWhiteSpace(searchDto.StationName))
                {
                    query = query.Where(x => x.Station.StationName.Contains(searchDto.StationName));
                }

                if (!string.IsNullOrWhiteSpace(searchDto.TeamName))
                {
                    query = query.Where(x => x.Team.TeamName.Contains(searchDto.TeamName));
                }

                if (!string.IsNullOrWhiteSpace(searchDto.ReporterName))
                {
                    query = query.Where(x => x.Reporter.DisplayName.Contains(searchDto.ReporterName) ||
                                           x.Reporter.Username.Contains(searchDto.ReporterName));
                }

                if (!string.IsNullOrWhiteSpace(searchDto.InspectorName))
                {
                    query = query.Where(x => x.Inspector.DisplayName.Contains(searchDto.InspectorName) ||
                                           x.Inspector.Username.Contains(searchDto.InspectorName));
                }

                if (!string.IsNullOrWhiteSpace(searchDto.WorkOrderName))
                {
                    query = query.Where(x => x.WorkOrder.OrderName.Contains(searchDto.WorkOrderName));
                }

                if (!string.IsNullOrWhiteSpace(searchDto.WorkOrderCode))
                {
                    query = query.Where(x => x.WorkOrder.OrderNumber.Contains(searchDto.WorkOrderCode));
                }

                if (!string.IsNullOrWhiteSpace(searchDto.TaskName))
                {
                    query = query.Where(x => x.Task.TaskName.Contains(searchDto.TaskName));
                }

                if (!string.IsNullOrWhiteSpace(searchDto.TaskCode))
                {
                    query = query.Where(x => x.Task.TaskNumber.Contains(searchDto.TaskCode));
                }

                // 时间范围搜索
                if (searchDto.ReportTimeStart.HasValue)
                {
                    query = query.Where(x => x.ReportTime >= searchDto.ReportTimeStart.Value);
                }

                if (searchDto.ReportTimeEnd.HasValue)
                {
                    query = query.Where(x => x.ReportTime <= searchDto.ReportTimeEnd.Value);
                }

                if (searchDto.InspectionTimeStart.HasValue)
                {
                    query = query.Where(x => x.InspectionTime >= searchDto.InspectionTimeStart.Value);
                }

                if (searchDto.InspectionTimeEnd.HasValue)
                {
                    query = query.Where(x => x.InspectionTime <= searchDto.InspectionTimeEnd.Value);
                }

                // 外键ID搜索
                if (searchDto.ProductId.HasValue)
                {
                    query = query.Where(x => x.ProductId == searchDto.ProductId.Value);
                }

                if (searchDto.ProcessStepId.HasValue)
                {
                    query = query.Where(x => x.ProcessStepId == searchDto.ProcessStepId.Value);
                }

                if (searchDto.StationId.HasValue)
                {
                    query = query.Where(x => x.StationId == searchDto.StationId.Value);
                }

                if (searchDto.TeamId.HasValue)
                {
                    query = query.Where(x => x.TeamId == searchDto.TeamId.Value);
                }

                if (searchDto.ReporterId.HasValue)
                {
                    query = query.Where(x => x.ReporterId == searchDto.ReporterId.Value);
                }

                if (searchDto.InspectorId.HasValue)
                {
                    query = query.Where(x => x.InspectorId == searchDto.InspectorId.Value);
                }

                if (searchDto.WorkOrderId.HasValue)
                {
                    query = query.Where(x => x.WorkOrderId == searchDto.WorkOrderId.Value);
                }

                if (searchDto.TaskId.HasValue)
                {
                    query = query.Where(x => x.TaskId == searchDto.TaskId.Value);
                }

                // 数量范围搜索
                if (searchDto.MinReportedQuantity.HasValue)
                {
                    query = query.Where(x => x.ReportedQuantity >= searchDto.MinReportedQuantity.Value);
                }

                if (searchDto.MaxReportedQuantity.HasValue)
                {
                    query = query.Where(x => x.ReportedQuantity <= searchDto.MaxReportedQuantity.Value);
                }

                if (searchDto.MinTestedQuantity.HasValue)
                {
                    query = query.Where(x => x.TestedQuantity >= searchDto.MinTestedQuantity.Value);
                }

                if (searchDto.MaxTestedQuantity.HasValue)
                {
                    query = query.Where(x => x.TestedQuantity <= searchDto.MaxTestedQuantity.Value);
                }

                // 分页查询
                var totalCount = await query.CountAsync();
                var entities = await query
                    .OrderByDescending(x => x.Id)
                    .Skip((searchDto.PageIndex - 1) * searchDto.PageSize)
                    .Take(searchDto.PageSize)
                    .ToListAsync();

                // 转换为DTO并填充关联信息
                var dtos = entities.Select(entity => new GetWorkReportInspectionDto
                {
                    Id = entity.Id,
                    InspectionCode = entity.InspectionCode,
                    InspectionName = entity.InspectionName,
                    InspectionType = entity.InspectionType,
                    Status = entity.Status,

                    // 外键
                    ProductId = entity.ProductId,
                    ProcessStepId = entity.ProcessStepId,
                    StationId = entity.StationId,
                    TeamId = entity.TeamId,
                    ReporterId = entity.ReporterId,
                    InspectorId = entity.InspectorId,
                    WorkOrderId = entity.WorkOrderId,
                    TaskId = entity.TaskId,

                    // 通过导航属性获取关联信息
                    ProductName = entity.Product?.MaterialName,
                    ProductCode = entity.Product?.MaterialNumber,
                    ProcessStepName = entity.ProcessStep?.ProcessStepName,
                    StationName = entity.Station?.StationName,
                    TeamName = entity.Team?.TeamName,
                    ReporterName = entity.Reporter?.DisplayName ?? entity.Reporter?.Username,
                    InspectorName = entity.Inspector?.DisplayName ?? entity.Inspector?.Username,
                    WorkOrderName = entity.WorkOrder?.OrderName,
                    WorkOrderCode = entity.WorkOrder?.OrderNumber,
                    TaskName = entity.Task?.TaskName,
                    TaskCode = entity.Task?.TaskNumber,

                    // 报工和质检信息
                    ReportedQuantity = entity.ReportedQuantity,
                    ReportTime = entity.ReportTime,
                    InspectionTime = entity.InspectionTime,
                    InspectionDepartment = entity.InspectionDepartment,
                    TestedQuantity = entity.TestedQuantity,
                    QualifiedQuantity = entity.QualifiedQuantity,
                    UnqualifiedQuantity = entity.UnqualifiedQuantity,
                    OverallResult = entity.OverallResult,
                    Remark = entity.Remark
                }).ToList();

                var pageResult = new PageResult<List<GetWorkReportInspectionDto>>
                {
                    Data = dtos,
                    TotalCount = totalCount,
                    TotalPage = (int)Math.Ceiling((double)totalCount / searchDto.PageSize)
                };

                return ApiResult<PageResult<List<GetWorkReportInspectionDto>>>.Success(pageResult, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<PageResult<List<GetWorkReportInspectionDto>>>.Fail($"获取质检记录列表失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 创建质检记录
        /// </summary>
        /// <param name="createDto">创建质检记录DTO</param>
        /// <returns>创建结果</returns>
        public async Task<ApiResult<GetWorkReportInspectionDto>> CreateAsync(CreateWorkReportInspectionDto createDto)
        {
            try
            {
                var entity = _mapper.Map<WorkReportInspectionEntity>(createDto);
                entity.Id = Guid.NewGuid();

                // 自动计算不合格数量
                if (entity.TestedQuantity.HasValue && entity.QualifiedQuantity.HasValue)
                {
                    entity.UnqualifiedQuantity = entity.TestedQuantity.Value - entity.QualifiedQuantity.Value;
                }

                // 根据合格率自动设置检测结果
                if (entity.TestedQuantity.HasValue && entity.QualifiedQuantity.HasValue && entity.TestedQuantity.Value > 0)
                {
                    var qualificationRate = (decimal)entity.QualifiedQuantity.Value / entity.TestedQuantity.Value;
                    entity.OverallResult = qualificationRate >= 1.0m ? "合格" : "不合格";
                }

                var result = await _workReportInspectionRepository.InsertAsync(entity);
                if (result)
                {
                    // 重新查询包含关联数据的实体
                    var createdEntity = await _workReportInspectionRepository.AsQueryable()
                        .Includes(x => x.Product)
                        .Includes(x => x.ProcessStep)
                        .Includes(x => x.Station)
                        .Includes(x => x.Team)
                        .Includes(x => x.Reporter)
                        .Includes(x => x.Inspector)
                        .Includes(x => x.WorkOrder)
                        .Includes(x => x.Task)
                        .FirstAsync(x => x.Id == entity.Id);

                    // 手动构建DTO，因为需要关联数据
                    var dto = new GetWorkReportInspectionDto
                    {
                        Id = createdEntity.Id,
                        InspectionCode = createdEntity.InspectionCode,
                        InspectionName = createdEntity.InspectionName,
                        InspectionType = createdEntity.InspectionType,
                        Status = createdEntity.Status,

                        // 外键
                        ProductId = createdEntity.ProductId,
                        ProcessStepId = createdEntity.ProcessStepId,
                        StationId = createdEntity.StationId,
                        TeamId = createdEntity.TeamId,
                        ReporterId = createdEntity.ReporterId,
                        InspectorId = createdEntity.InspectorId,
                        TaskId = createdEntity.TaskId,

                        // 关联信息
                        ProductName = createdEntity.Product?.MaterialName,
                        ProductCode = createdEntity.Product?.MaterialNumber,
                        ProcessStepName = createdEntity.ProcessStep?.ProcessStepName,
                        StationName = createdEntity.Station?.StationName,
                        TeamName = createdEntity.Team?.TeamName,
                        ReporterName = createdEntity.Reporter?.DisplayName,
                        InspectorName = createdEntity.Inspector?.DisplayName,
                        WorkOrderName = createdEntity.WorkOrder?.OrderName,
                        TaskName = createdEntity.Task?.TaskName,

                        // 报工和质检信息
                        ReportedQuantity = createdEntity.ReportedQuantity,
                        ReportTime = createdEntity.ReportTime,
                        InspectionTime = createdEntity.InspectionTime,
                        InspectionDepartment = createdEntity.InspectionDepartment,
                        TestedQuantity = createdEntity.TestedQuantity,
                        QualifiedQuantity = createdEntity.QualifiedQuantity,
                        UnqualifiedQuantity = createdEntity.UnqualifiedQuantity,
                        OverallResult = createdEntity.OverallResult,
                        Remark = createdEntity.Remark
                    };

                    return ApiResult<GetWorkReportInspectionDto>.Success(dto, ResultCode.Success);
                }

                return ApiResult<GetWorkReportInspectionDto>.Fail("创建质检记录失败", ResultCode.Error);
            }
            catch (Exception ex)
            {
                return ApiResult<GetWorkReportInspectionDto>.Fail($"创建质检记录失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 更新质检记录
        /// </summary>
        /// <param name="updateDto">更新质检记录DTO</param>
        /// <returns>更新结果</returns>
        public async Task<ApiResult<GetWorkReportInspectionDto>> UpdateAsync(UpdateWorkReportInspectionDto updateDto)
        {
            try
            {
                var entity = await _workReportInspectionRepository.GetByIdAsync(updateDto.Id);
                if (entity == null)
                {
                    return ApiResult<GetWorkReportInspectionDto>.Fail("质检记录不存在", ResultCode.NotFound);
                }

                // 简化更新逻辑
                _mapper.Map(updateDto, entity);

                var result = await _workReportInspectionRepository.UpdateAsync(entity);
                if (result)
                {
                    // 重新查询包含关联数据的实体
                    var updatedEntity = await _workReportInspectionRepository.AsQueryable()
                        .Includes(x => x.Product)
                        .Includes(x => x.ProcessStep)
                        .Includes(x => x.Station)
                        .Includes(x => x.Team)
                        .Includes(x => x.Reporter)
                        .Includes(x => x.Inspector)
                        .Includes(x => x.WorkOrder)
                        .Includes(x => x.Task)
                        .FirstAsync(x => x.Id == entity.Id);

                    // 手动构建DTO
                    var dto = new GetWorkReportInspectionDto
                    {
                        Id = updatedEntity.Id,
                        InspectionCode = updatedEntity.InspectionCode,
                        InspectionName = updatedEntity.InspectionName,
                        InspectionType = updatedEntity.InspectionType,
                        Status = updatedEntity.Status,

                        // 外键
                        ProductId = updatedEntity.ProductId,
                        ProcessStepId = updatedEntity.ProcessStepId,
                        StationId = updatedEntity.StationId,
                        TeamId = updatedEntity.TeamId,
                        ReporterId = updatedEntity.ReporterId,
                        InspectorId = updatedEntity.InspectorId,
                        TaskId = updatedEntity.TaskId,

                        // 关联信息
                        ProductName = updatedEntity.Product?.MaterialName,
                        ProductCode = updatedEntity.Product?.MaterialNumber,
                        ProcessStepName = updatedEntity.ProcessStep?.ProcessStepName,
                        StationName = updatedEntity.Station?.StationName,
                        TeamName = updatedEntity.Team?.TeamName,
                        ReporterName = updatedEntity.Reporter?.DisplayName,
                        InspectorName = updatedEntity.Inspector?.DisplayName,
                        WorkOrderName = updatedEntity.WorkOrder?.OrderName,
                        TaskName = updatedEntity.Task?.TaskName,

                        // 报工和质检信息
                        ReportedQuantity = updatedEntity.ReportedQuantity,
                        ReportTime = updatedEntity.ReportTime,
                        InspectionTime = updatedEntity.InspectionTime,
                        InspectionDepartment = updatedEntity.InspectionDepartment,
                        TestedQuantity = updatedEntity.TestedQuantity,
                        QualifiedQuantity = updatedEntity.QualifiedQuantity,
                        UnqualifiedQuantity = updatedEntity.UnqualifiedQuantity,
                        OverallResult = updatedEntity.OverallResult,
                        Remark = updatedEntity.Remark
                    };

                    return ApiResult<GetWorkReportInspectionDto>.Success(dto, ResultCode.Success);
                }

                return ApiResult<GetWorkReportInspectionDto>.Fail("更新质检记录失败", ResultCode.Error);
            }
            catch (Exception ex)
            {
                return ApiResult<GetWorkReportInspectionDto>.Fail($"更新质检记录失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 删除质检记录
        /// </summary>
        /// <param name="id">质检记录Id</param>
        /// <returns>删除结果</returns>
        public async Task<ApiResult<bool>> DeleteAsync(Guid id)
        {
            try
            {
                var result = await _workReportInspectionRepository.SoftDeleteAsync(id);
                return ApiResult<bool>.Success(result, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除质检记录失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 批量删除质检记录
        /// </summary>
        /// <param name="ids">质检记录Id列表</param>
        /// <returns>删除结果</returns>
        public async Task<ApiResult<bool>> BatchDeleteAsync(List<Guid> ids)
        {
            try
            {
                if (ids == null || !ids.Any())
                {
                    return ApiResult<bool>.Fail("请选择要删除的记录", ResultCode.ValidationError);
                }

                var result = await _workReportInspectionRepository.SoftDeleteRangeAsync(ids.Cast<object>().ToArray());
                return ApiResult<bool>.Success(result, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"批量删除质检记录失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 执行质检操作
        /// </summary>
        /// <param name="id">质检记录Id</param>
        /// <param name="updateDto">质检更新信息</param>
        /// <returns>质检结果</returns>
        public async Task<ApiResult<GetWorkReportInspectionDto>> PerformInspectionAsync(Guid id, UpdateWorkReportInspectionDto updateDto)
        {
            try
            {
                updateDto.Id = id;
                updateDto.InspectionTime = DateTime.Now;
                updateDto.Status = "已质检";

                return await UpdateAsync(updateDto);
            }
            catch (Exception ex)
            {
                return ApiResult<GetWorkReportInspectionDto>.Fail($"执行质检操作失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 获取质检统计信息
        /// </summary>
        /// <param name="searchDto">搜索条件</param>
        /// <returns>统计信息</returns>
        public async Task<ApiResult<object>> GetStatisticsAsync(WorkReportInspectionAdvancedSearchDto searchDto)
        {
            try
            {
                var entities = await _workReportInspectionRepository.GetAllAsync();

                var statistics = new
                {
                    TotalCount = entities.Count,
                    InspectedCount = entities.Count(x => x.Status == "已质检"),
                    UnInspectedCount = entities.Count(x => x.Status == "未质检")
                };

                return ApiResult<object>.Success(statistics, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<object>.Fail($"获取质检统计信息失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 检查质检相关表的数据情况
        /// </summary>
        /// <returns>各关联表的数据统计</returns>
        public async Task<ApiResult<object>> CheckRelatedDataAsync()
        {
            try
            {
                var db = _workReportInspectionRepository.GetDbContext();

                var result = new
                {
                    // 质检记录表本身
                    WorkReportInspections = await db.Queryable<WorkReportInspectionEntity>().CountAsync(),

                    // 关联的基础数据表数量统计
                    RelatedTables = new
                    {
                        Products = await db.Queryable<SqlsugarService.Domain.Materials.ProductEntity>().CountAsync(),
                        ProcessSteps = await db.Queryable<SqlsugarService.Domain.Craftsmanship.ProcessStep>().CountAsync(),
                        Stations = await db.Queryable<SqlsugarService.Domain.Station.StationEntity>().CountAsync(),
                        Teams = await db.Queryable<SqlsugarService.Domain.Team.TeamEntity>().CountAsync(),
                        Users = await db.Queryable<SqlsugarService.Domain.Users>().CountAsync(),
                        WorkOrderTasks = await db.Queryable<SqlsugarService.Domain.Plan.WorkOrderTaskEntity>().CountAsync()
                    },

                    // 数据完整性检查
                    DataIntegrityCheck = new
                    {
                        HasProducts = await db.Queryable<SqlsugarService.Domain.Materials.ProductEntity>().AnyAsync(),
                        HasProcessSteps = await db.Queryable<SqlsugarService.Domain.Craftsmanship.ProcessStep>().AnyAsync(),
                        HasStations = await db.Queryable<SqlsugarService.Domain.Station.StationEntity>().AnyAsync(),
                        HasTeams = await db.Queryable<SqlsugarService.Domain.Team.TeamEntity>().AnyAsync(),
                        HasUsers = await db.Queryable<SqlsugarService.Domain.Users>().AnyAsync(),

                        // 检查是否可以创建质检记录
                        CanCreateInspection =
                            await db.Queryable<SqlsugarService.Domain.Materials.ProductEntity>().AnyAsync() &&
                            await db.Queryable<SqlsugarService.Domain.Craftsmanship.ProcessStep>().AnyAsync() &&
                            await db.Queryable<SqlsugarService.Domain.Station.StationEntity>().AnyAsync() &&
                            await db.Queryable<SqlsugarService.Domain.Team.TeamEntity>().AnyAsync() &&
                            await db.Queryable<SqlsugarService.Domain.Users>().AnyAsync()
                    },

                    CheckedAt = DateTime.Now
                };

                return ApiResult<object>.Success(result, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<object>.Fail($"检查关联数据失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 获取简单质检记录列表（不依赖关联数据）
        /// </summary>
        /// <returns>质检记录基础信息</returns>
        public async Task<ApiResult<object>> GetSimpleListAsync()
        {
            try
            {
                var entities = await _workReportInspectionRepository.GetAllAsync();

                var result = entities.Select(x => new
                {
                    x.Id,
                    x.InspectionCode,
                    x.InspectionName,
                    x.InspectionType,
                    x.Status,
                    x.ProductId,
                    x.ProcessStepId,
                    x.StationId,
                    x.TeamId,
                    x.ReporterId,
                    x.InspectorId,
                    x.ReportedQuantity,
                    x.ReportTime,
                    x.TestedQuantity,
                    x.QualifiedQuantity,
                    x.UnqualifiedQuantity,
                    x.OverallResult,
                    x.Remark
                }).ToList();

                return ApiResult<object>.Success(result, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<object>.Fail($"获取简单质检记录列表失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 分页获取质检记录列表
        /// </summary>
        /// <param name="inspectionCode">检验单号（可选）</param>
        /// <param name="inspectionName">检验单名称（可选）</param>
        /// <param name="status">状态过滤（可选）</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页数量</param>
        /// <returns>分页的质检记录列表</returns>
        public async Task<ApiResult<PageResult<List<GetWorkReportInspectionDto>>>> GetPagedListAsync(string? planNumber, string? productName, string? status, int pageIndex, int pageSize)
        {
            try
            {
                // 构建查询条件
                var query = _workReportInspectionRepository.AsQueryable();

                // 包含关联数据 - 完整的联查
                query = query.Includes(x => x.Product)
                           .Includes(x => x.ProcessStep)
                           .Includes(x => x.Station)
                           .Includes(x => x.Team)
                           .Includes(x => x.Reporter)
                           .Includes(x => x.Inspector)
                           .Includes(x => x.ProductionPlan)
                           .Includes(x => x.ProductionOrder)
                           .Includes(x => x.Task);

                // 计划编号过滤（模糊查询）
                if (!string.IsNullOrEmpty(planNumber?.Trim()))
                {
                    query = query.Where(x => 
                        (x.ProductionPlan != null && x.ProductionPlan.PlanNumber.Contains(planNumber.Trim())) ||
                        (x.ProductionOrder != null && x.ProductionOrder.OrderNumber.Contains(planNumber.Trim())) ||
                        (x.Product != null && x.Product.MaterialNumber.Contains(planNumber.Trim())));
                }

                // 产品名称过滤（模糊查询）
                if (!string.IsNullOrEmpty(productName?.Trim()))
                {
                    query = query.Where(x => x.Product != null && 
                        x.Product.MaterialName.Contains(productName.Trim()));
                }

                // 状态过滤
                if (!string.IsNullOrEmpty(status?.Trim()))
                {
                    query = query.Where(x => x.Status == status.Trim());
                }

                // 分页查询
                var totalCount = await query.CountAsync();
                var entities = await query
                    .OrderByDescending(x => x.ReportTime)
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // 转换为DTO
                var dtos = entities.Select(entity => new GetWorkReportInspectionDto
                {
                    Id = entity.Id,
                    InspectionCode = entity.InspectionCode,
                    InspectionName = entity.InspectionName,
                    InspectionType = entity.InspectionType,
                    Status = entity.Status,

                    // 外键
                    ProductId = entity.ProductId,
                    ProcessStepId = entity.ProcessStepId,
                    StationId = entity.StationId,
                    TeamId = entity.TeamId,
                    ReporterId = entity.ReporterId,
                    InspectorId = entity.InspectorId,
                    WorkOrderId = entity.WorkOrderId,
                    TaskId = entity.TaskId,

                    // 关联信息
                    ProductName = entity.Product?.MaterialName,
                    ProductCode = entity.Product?.MaterialNumber,
                    ProcessStepName = entity.ProcessStep?.ProcessStepName,
                    StationName = entity.Station?.StationName,
                    TeamName = entity.Team?.TeamName,
                    ReporterName = entity.Reporter?.DisplayName,
                    InspectorName = entity.Inspector?.DisplayName,
                    
                    // 生产计划信息
                    ProductionPlanNumber = entity.ProductionPlan?.PlanNumber,
                    ProductionPlanName = entity.ProductionPlan?.PlanName,
                    PlanNumber = entity.ProductionPlan?.PlanNumber, // 兼容原有字段
                    
                    // 生产工单信息
                    ProductionOrderNumber = entity.ProductionOrder?.OrderNumber,
                    ProductionOrderName = entity.ProductionOrder?.OrderName,
                    
                    // 任务信息
                    TaskName = entity.Task?.TaskName,
                    TaskCode = entity.Task?.TaskNumber,

                    // 报工和质检信息
                    ReportedQuantity = entity.ReportedQuantity,
                    ReportTime = entity.ReportTime,
                    TestedQuantity = entity.TestedQuantity,
                    QualifiedQuantity = entity.QualifiedQuantity,
                    UnqualifiedQuantity = entity.UnqualifiedQuantity,
                    InspectionTime = entity.InspectionTime,
                    OverallResult = entity.OverallResult,
                    Remark = entity.Remark
                }).ToList();

                var pageResult = new PageResult<List<GetWorkReportInspectionDto>>
                {
                    Data = dtos,
                    TotalCount = totalCount,
                    TotalPage = (int)Math.Ceiling((double)totalCount / pageSize)
                };

                return ApiResult<PageResult<List<GetWorkReportInspectionDto>>>.Success(pageResult, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<PageResult<List<GetWorkReportInspectionDto>>>.Fail(
                    $"获取质检记录分页列表失败: {ex.Message}", 
                    ResultCode.Error);
            }
        }
    }
}
