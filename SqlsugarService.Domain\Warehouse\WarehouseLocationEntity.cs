using SqlSugar;
using SqlsugarService.Domain.Common;
using System;

namespace SqlsugarService.Domain.Warehouse
{
    /// <summary>
    /// 库位实体类
    /// </summary>
    public class WarehouseLocationEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 库位编码
        /// </summary>
        public string LocationCode { get; set; }

        /// <summary>
        /// 库位名称
        /// </summary>
        public string LocationName { get; set; }

        /// <summary>
        /// 面积
        /// </summary>
        public int Area { get; set; }

        /// <summary>
        /// 最大载重量
        /// </summary>
        public int MaxLoad { get; set; }

        /// <summary>
        /// 库位位置X
        /// </summary>
        public int PositionX { get; set; }

        /// <summary>
        /// 库位位置Y
        /// </summary>
        public int PositionY { get; set; }

        /// <summary>
        /// 库位位置Z
        /// </summary>
        public int PositionZ { get; set; }

        /// <summary>
        /// 所属仓库Id
        /// </summary>
        public Guid WarehouseId { get; set; }

        /// <summary>
        /// 所属仓库名称
        /// </summary>
        public string WarehouseName { get; set; }

        /// <summary>
        /// 所属库区Id
        /// </summary>
        public Guid AreaId { get; set; }

        /// <summary>
        /// 所属库区名称
        /// </summary>
        public string AreaName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }
} 