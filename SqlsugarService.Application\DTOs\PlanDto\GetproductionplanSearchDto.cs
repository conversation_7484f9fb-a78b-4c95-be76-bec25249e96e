﻿using SqlsugarService.Application.Until;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.DTOs.PlanDto
{
    public class GetproductionplanSearchDto:Seach
    {
        /// <summary>
        /// 计划编号/计划名称
        /// </summary>
        public string? PlanNumber { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }
        /// <summary>
        /// 状态 未分解0 已分解1 已完成2 已关闭3 已撤回4 进行中5
        /// </summary> 
        public int? Status { get; set; }
    }
}
