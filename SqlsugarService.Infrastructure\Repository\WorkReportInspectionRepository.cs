using SqlsugarService.Domain.QualityInspection;
using SqlsugarService.Infrastructure.IRepository;
using SqlSugar;

namespace SqlsugarService.Infrastructure.Repository
{
    /// <summary>
    /// 质检仓储实现
    /// </summary>
    public class WorkReportInspectionRepository : BaseRepository<WorkReportInspectionEntity>, IWorkReportInspectionRepository
    {
        public WorkReportInspectionRepository(ISqlSugarClient db) : base(db)
        {
        }

        /// <summary>
        /// 插入并返回实体
        /// </summary>
        /// <param name="entity">实体对象</param>
        /// <returns>插入后的实体</returns>
        public new async Task<WorkReportInspectionEntity> InsertReturnEntityAsync(WorkReportInspectionEntity entity)
        {
            return await Context.Insertable(entity).ExecuteReturnEntityAsync();
        }

        /// <summary>
        /// 根据ID删除
        /// </summary>
        /// <param name="id">ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteByIdAsync(Guid id)
        {
            return await Context.Deleteable<WorkReportInspectionEntity>().In(id).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="ids">ID数组</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteByIdsAsync(Guid[] ids)
        {
            return await Context.Deleteable<WorkReportInspectionEntity>().In(ids).ExecuteCommandAsync() > 0;
        }
    }
}