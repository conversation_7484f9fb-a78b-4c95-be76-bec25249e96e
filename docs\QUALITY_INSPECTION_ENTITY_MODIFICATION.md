# 质检实体修改说明

## 概述

根据MES业务逻辑和效果图要求，对质检实体 `WorkReportInspectionEntity` 进行了全面的修改和扩展，以支持完整的质检业务流程。

## 修改内容

### 1. 实体类修改 (`WorkReportInspectionEntity`)

#### 新增字段

**MES业务信息字段：**
- `WorkOrderId` - 工单Id
- `TaskId` - 任务Id  
- `TaskManagerId` - 任务负责人Id
- `WorkOrderName` - 工单名称
- `WorkOrderCode` - 工单编号
- `TaskName` - 任务名称
- `TaskCode` - 任务编号
- `StationName` - 站点名称
- `ProcessStepName` - 工序名称
- `ProductName` - 产品名称
- `ProductCode` - 产品编号
- `TeamName` - 班组名称
- `TaskManagerName` - 任务负责人姓名
- `ReporterName` - 报工人员姓名
- `InspectorName` - 检验人员姓名

**计算字段：**
- `QualificationRate` - 合格率（自动计算，百分比）

#### 字段修改
- `InspectorId` - 改为可空类型，支持未分配检验人员的情况
- 多个字符串字段改为可空类型，提高数据灵活性

### 2. DTO类扩展

#### 新增DTO
- `CreateWorkReportInspectionDto` - 创建质检记录DTO，包含完整的验证规则
- `UpdateWorkReportInspectionDto` - 更新质检记录DTO，支持部分字段更新
- `WorkReportInspectionTestDto` - 测试数据生成DTO

#### 修改DTO
- `GetWorkReportInspectionDto` - 添加所有新增字段，包含合格率计算
- `GetWorkReportInspectionSearchDto` - 扩展搜索条件，支持多维度筛选

### 3. 服务层实现

#### 接口扩展 (`IWorkReportInspectionService`)
- `GetListAsync` - 获取质检记录列表（支持复杂搜索）
- `GetByIdAsync` - 获取单条记录详情
- `CreateAsync` - 创建质检记录
- `UpdateAsync` - 更新质检记录
- `DeleteAsync` - 删除质检记录
- `BatchDeleteAsync` - 批量删除
- `PerformInspectionAsync` - 执行质检操作
- `GetStatisticsAsync` - 获取统计信息

#### 服务实现 (`WorkReportInspectionService`)
- 完整的CRUD操作实现
- 自动计算不合格数量和合格率
- 根据合格率自动设置检测结果
- 复杂的搜索条件支持
- 统计信息计算

### 4. API控制器

#### 新增控制器 (`WorkReportInspectionController`)
- 提供完整的RESTful API接口
- 支持分页查询和复杂搜索
- 提供质检统计接口
- 提供选项数据接口（检验类型、状态、结果等）

### 5. 配置更新

#### AutoMapper配置
- 添加质检实体与DTO之间的映射配置
- 支持条件映射，避免null值覆盖

#### 依赖注入配置
- 注册质检服务接口和实现
- 确保服务正确注入到容器中

## 效果图字段对应关系

| 效果图字段 | 实体字段 | 说明 |
|-----------|----------|------|
| 序号 | - | 前端生成 |
| 检验单名称 | InspectionName | 质检单名称 |
| 检验单号 | InspectionCode | 质检单编号 |
| 工单名称 | WorkOrderName | 关联工单 |
| 任务名称 | TaskName | 关联任务 |
| 站点名称 | StationName | 作业站点 |
| 工序名称 | ProcessStepName | 作业工序 |
| 产品名称 | ProductName | 被检产品 |
| 产品编号 | ProductCode | 产品编码 |
| 班组名称 | TeamName | 作业班组 |
| 任务负责人 | TaskManagerName | 任务负责人 |
| 报工人员 | ReporterName | 报工操作员 |
| 报工数量 | ReportedQuantity | 报工数量 |
| 报工时间 | ReportTime | 报工时间 |
| 检验时间 | InspectionTime | 质检时间 |
| 检验类型 | InspectionType | 检验类型 |
| 检验部门 | InspectionDepartment | 质检部门 |
| 检验人 | InspectorName | 质检员 |
| 检测数量 | TestedQuantity | 抽检数量 |
| 合格数量 | QualifiedQuantity | 合格数量 |
| 不合格数量 | UnqualifiedQuantity | 不合格数量 |
| 合格率 | QualificationRate | 自动计算 |
| 检测结果 | OverallResult | 质检结果 |
| 状态 | Status | 质检状态 |

## 业务逻辑特性

### 1. 自动计算功能
- **不合格数量**：检测数量 - 合格数量
- **合格率**：合格数量 / 检测数量 × 100%
- **检测结果**：合格率 >= 100% 时为"合格"，否则为"不合格"

### 2. 状态管理
- **未质检**：初始状态，等待质检
- **已质检**：完成质检，有检测结果
- **已完检**：质检流程完全结束

### 3. 检验类型支持
- 首检、巡检、末检、自检、专检、抽检

### 4. 数据验证
- 必填字段验证
- 数据范围验证
- 字符串长度限制
- 业务逻辑验证

## API接口说明

### 基础CRUD接口
- `POST /api/WorkReportInspection/list` - 获取质检记录列表
- `GET /api/WorkReportInspection/{id}` - 获取质检记录详情
- `POST /api/WorkReportInspection` - 创建质检记录
- `PUT /api/WorkReportInspection` - 更新质检记录
- `DELETE /api/WorkReportInspection/{id}` - 删除质检记录

### 业务操作接口
- `POST /api/WorkReportInspection/{id}/perform-inspection` - 执行质检
- `POST /api/WorkReportInspection/batch-delete` - 批量删除
- `POST /api/WorkReportInspection/statistics` - 获取统计信息

### 辅助数据接口
- `GET /api/WorkReportInspection/inspection-types` - 获取检验类型选项
- `GET /api/WorkReportInspection/status-options` - 获取状态选项
- `GET /api/WorkReportInspection/result-options` - 获取检测结果选项

## 使用示例

### 创建质检记录
```csharp
var createDto = new CreateWorkReportInspectionDto
{
    InspectionCode = "JYDH0210",
    InspectionName = "任务检验单一",
    InspectionType = "首检",
    ProductId = productId,
    ProcessStepId = processStepId,
    // ... 其他字段
};

var result = await _workReportInspectionService.CreateAsync(createDto);
```

### 执行质检
```csharp
var updateDto = new UpdateWorkReportInspectionDto
{
    TestedQuantity = 50,
    QualifiedQuantity = 45,
    InspectionDepartment = "质检部门",
    InspectorName = "张三"
};

var result = await _workReportInspectionService.PerformInspectionAsync(id, updateDto);
```

## 注意事项

1. **数据一致性**：确保关联的产品、工序、站点等实体存在
2. **权限控制**：根据实际需求添加权限验证
3. **数据备份**：重要质检数据建议定期备份
4. **性能优化**：大数据量查询时注意分页和索引优化
5. **扩展性**：预留了扩展字段，便于后续功能增强

## 后续优化建议

1. 添加质检项目明细表，支持多项目检测
2. 集成工作流引擎，支持质检审批流程
3. 添加质检报告生成功能
4. 集成消息通知，质检异常时自动通知相关人员
5. 添加质检数据分析和趋势预测功能
