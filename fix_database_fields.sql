-- 快速修复质检表缺失字段
-- 执行此脚本来添加缺失的数据库字段

-- 检查表是否存在
DO $$
BEGIN
    -- 检查表名（可能是不同的命名约定）
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'workreportinspectionentity') THEN
        -- 添加生产计划ID字段
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'workreportinspectionentity' AND column_name = 'productionplanid') THEN
            ALTER TABLE workreportinspectionentity ADD COLUMN productionplanid UUID NULL;
            RAISE NOTICE '已添加 productionplanid 字段';
        END IF;
        
        -- 添加生产工单ID字段
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'workreportinspectionentity' AND column_name = 'productionorderid') THEN
            ALTER TABLE workreportinspectionentity ADD COLUMN productionorderid UUID NULL;
            RAISE NOTICE '已添加 productionorderid 字段';
        END IF;
        
        -- 添加任务ID字段
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'workreportinspectionentity' AND column_name = 'taskid') THEN
            ALTER TABLE workreportinspectionentity ADD COLUMN taskid UUID NULL;
            RAISE NOTICE '已添加 taskid 字段';
        END IF;
    ELSE
        RAISE NOTICE '表 workreportinspectionentity 不存在，请检查表名';
    END IF;
END $$;

-- 检查其他可能的表名
DO $$
BEGIN
    -- 检查是否是 WorkReportInspection 表名
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'workreportinspection') THEN
        -- 添加生产计划ID字段
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'workreportinspection' AND column_name = 'productionplanid') THEN
            ALTER TABLE workreportinspection ADD COLUMN productionplanid UUID NULL;
            RAISE NOTICE '已添加 productionplanid 字段到 workreportinspection 表';
        END IF;
        
        -- 添加生产工单ID字段
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'workreportinspection' AND column_name = 'productionorderid') THEN
            ALTER TABLE workreportinspection ADD COLUMN productionorderid UUID NULL;
            RAISE NOTICE '已添加 productionorderid 字段到 workreportinspection 表';
        END IF;
        
        -- 添加任务ID字段
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'workreportinspection' AND column_name = 'taskid') THEN
            ALTER TABLE workreportinspection ADD COLUMN taskid UUID NULL;
            RAISE NOTICE '已添加 taskid 字段到 workreportinspection 表';
        END IF;
    END IF;
END $$;

-- 显示当前表结构
SELECT 
    table_name,
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name LIKE '%workreportinspection%'
ORDER BY table_name, ordinal_position;