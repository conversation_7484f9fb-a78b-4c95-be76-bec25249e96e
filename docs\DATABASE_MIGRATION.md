# 数据库迁移指南

本文档详细说明如何执行AuthService项目的数据库迁移。

## 📋 前置要求

### 1. 数据库环境
- **PostgreSQL 12+** 已安装并运行
- 数据库服务器可访问
- 具有创建数据库和表的权限

### 2. 开发环境
- **.NET 9.0 SDK** 已安装
- **FluentMigrator CLI工具** (会自动安装)

## 🚀 快速开始

### 1. 准备数据库
确保PostgreSQL服务正在运行，并创建数据库：

```sql
-- 连接到PostgreSQL
psql -U postgres -h localhost

-- 创建数据库
CREATE DATABASE authservice;

-- 创建用户（可选）
CREATE USER authservice_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE authservice TO authservice_user;
```

### 2. 配置连接字符串
更新 `AuthService.Api/appsettings.Development.json`：

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=authservice;Username=postgres;Password=your_password"
  }
}
```

### 3. 执行迁移

#### 方法一：通过应用程序启动（推荐）
```bash
cd AuthService.Api
dotnet run
```
应用程序启动时会自动执行数据库迁移。

#### 方法二：使用迁移脚本
```bash
# Windows PowerShell
.\scripts\migrate-database.ps1

# Linux/macOS
chmod +x scripts/migrate-database.sh
./scripts/migrate-database.sh
```

#### 方法三：手动执行
```bash
# 1. 安装FluentMigrator工具
dotnet tool install -g FluentMigrator.DotNet.Cli

# 2. 构建项目
cd AuthService.Api
dotnet build --configuration Release

# 3. 执行迁移
dotnet fm migrate -p postgres -c "Host=localhost;Port=5432;Database=authservice;Username=postgres;Password=your_password" -a "bin/Release/net9.0/AuthService.Infrastructure.dll"
```

## 📊 迁移详情

### 当前迁移列表

#### 001_CreateUsersTable (版本: 20241214001)
创建用户表，包含以下字段：
- **基础字段**: Id, Username, Email, PasswordHash, PasswordSalt
- **用户信息**: DisplayName, AvatarUrl, PhoneNumber
- **验证状态**: IsEmailVerified, IsPhoneVerified, IsActive, IsLocked
- **登录信息**: LastLoginAt, LastLoginIp, FailedLoginAttempts
- **权限字段**: Roles, Permissions (JSON格式)
- **扩展字段**: Metadata, TenantId
- **审计字段**: CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsDeleted, DeletedAt, DeletedBy

**索引**:
- 主键: PK_Users (Id)
- 唯一索引: UQ_Users_Username, UQ_Users_Email
- 性能索引: IX_Users_TenantId, IX_Users_IsActive_IsDeleted, IX_Users_CreatedAt

#### 002_CreateDynamicApiEndpointsTable (版本: 20241214002)
创建动态API端点表，包含以下字段：
- **基础字段**: Id, Name, Description, Method, PathTemplate
- **目标配置**: TargetService, TargetUrl
- **权限配置**: IsEnabled, RequireAuthentication, RequiredRoles, RequiredPermissions
- **性能配置**: RateLimitPerMinute, TimeoutSeconds, RetryCount, CacheSeconds
- **转换配置**: Headers, QueryParameters, RequestTransformation, ResponseTransformation
- **版本管理**: Version, Tags, TenantId, Priority
- **统计字段**: LastAccessedAt, AccessCount
- **审计字段**: 同Users表

**索引**:
- 主键: PK_DynamicApiEndpoints (Id)
- 唯一索引: UQ_DynamicApiEndpoints_TenantId_PathTemplate_Method
- 性能索引: 多个复合索引用于路由匹配和查询优化

## 🔧 高级操作

### 查看迁移状态
```bash
dotnet fm list migrations -p postgres -c "your_connection_string" -a "path_to_assembly"
```

### 回滚迁移
```bash
# 回滚到指定版本
dotnet fm rollback -p postgres -c "your_connection_string" -a "path_to_assembly" --version 20241214001

# 使用脚本回滚
.\scripts\migrate-database.ps1 -Action down -Version 20241214001
```

### 验证迁移
```bash
dotnet fm validate -p postgres -c "your_connection_string" -a "path_to_assembly"
```

## 🐛 故障排除

### 常见问题

#### 1. 连接失败
**错误**: `Npgsql.NpgsqlException: Connection refused`
**解决方案**:
- 检查PostgreSQL服务是否运行
- 验证连接字符串中的主机和端口
- 检查防火墙设置

#### 2. 权限不足
**错误**: `permission denied for database`
**解决方案**:
- 确保用户有创建表的权限
- 使用超级用户执行迁移
- 检查数据库用户权限设置

#### 3. 迁移已存在
**错误**: `Migration with version XXX already exists`
**解决方案**:
- 检查数据库中的VersionInfo表
- 清理重复的迁移记录
- 使用不同的版本号

#### 4. 程序集加载失败
**错误**: `Could not load assembly`
**解决方案**:
- 确保项目已正确构建
- 检查程序集路径是否正确
- 验证.NET版本兼容性

### 手动清理数据库
如果需要完全重置数据库：

```sql
-- 连接到数据库
\c authservice

-- 删除所有表
DROP TABLE IF EXISTS "DynamicApiEndpoints";
DROP TABLE IF EXISTS "Users";
DROP TABLE IF EXISTS "VersionInfo";

-- 或者删除整个数据库重新创建
-- DROP DATABASE authservice;
-- CREATE DATABASE authservice;
```

## 📝 创建新迁移

### 1. 创建迁移类
在 `AuthService.Infrastructure/Migrations/` 目录下创建新文件：

```csharp
using FluentMigrator;

namespace AuthService.Infrastructure.Migrations;

[Migration(20241214003)] // 使用新的版本号
public class AddNewTable : Migration
{
    public override void Up()
    {
        Create.Table("NewTable")
            .WithColumn("Id").AsGuid().PrimaryKey()
            .WithColumn("Name").AsString(100).NotNullable();
    }

    public override void Down()
    {
        Delete.Table("NewTable");
    }
}
```

### 2. 版本号规则
使用格式: `YYYYMMDDNNN`
- YYYY: 年份
- MM: 月份  
- DD: 日期
- NNN: 当日序号 (001, 002, 003...)

### 3. 最佳实践
- 每个迁移只做一件事
- 提供完整的Up和Down方法
- 添加适当的索引
- 包含详细的注释
- 测试迁移的可逆性

## 🔒 生产环境注意事项

### 1. 备份策略
```bash
# 执行迁移前备份数据库
pg_dump -U postgres -h localhost authservice > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. 迁移检查清单
- [ ] 在测试环境验证迁移
- [ ] 备份生产数据库
- [ ] 检查迁移脚本
- [ ] 准备回滚计划
- [ ] 监控迁移过程
- [ ] 验证迁移结果

### 3. 性能考虑
- 大表迁移可能需要较长时间
- 考虑在维护窗口执行
- 监控数据库性能
- 准备必要的索引优化

## 📞 获取帮助

如果遇到问题：
1. 检查日志文件
2. 查看PostgreSQL错误日志
3. 参考FluentMigrator文档
4. 联系开发团队
