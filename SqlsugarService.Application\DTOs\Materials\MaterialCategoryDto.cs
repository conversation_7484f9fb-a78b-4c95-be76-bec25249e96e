using System;
using System.ComponentModel.DataAnnotations;

namespace SqlsugarService.Application.DTOs.Materials
{
    /// <summary>
    /// 物料分类数据传输对象
    /// </summary>
    public class MaterialCategoryDto
    {
        /// <summary>
        /// 分类名称
        /// </summary>
        [Required(ErrorMessage = "分类名称不能为空")]
        [StringLength(50, ErrorMessage = "分类名称长度不能超过50个字符")]
        public string Name { get; set; }

        /// <summary>
        /// 父级分类Id（根节点为null）
        /// </summary>
        public Guid? ParentId { get; set; } = Guid.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }
} 