﻿using SqlsugarService.Application.DTOs.WorkReportInspectionDto;
using SqlsugarService.Application.Until;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.IService.WorkReportInspection
{
    /// <summary>
    /// 报工质检服务接口
    /// </summary>
    public interface IWorkReportInspectionService
    {
        /// <summary>
        /// 获取质检记录列表
        /// </summary>
        /// <param name="searchDto">搜索条件</param>
        /// <returns>质检记录列表</returns>
        Task<ApiResult<PageResult<List<GetWorkReportInspectionDto>>>> GetListAsync(GetWorkReportInspectionSearchDto searchDto);

        /// <summary>
        /// 获取质检记录列表 (高级搜索，支持关联表字段搜索)
        /// </summary>
        /// <param name="searchDto">高级搜索条件</param>
        /// <returns>质检记录列表</returns>
        Task<ApiResult<PageResult<List<GetWorkReportInspectionDto>>>> GetAdvancedListAsync(WorkReportInspectionAdvancedSearchDto searchDto);

        /// <summary>
        /// 根据Id获取质检记录详情
        /// </summary>
        /// <param name="id">质检记录Id</param>
        /// <returns>质检记录详情</returns>
        Task<ApiResult<GetWorkReportInspectionDto>> GetByIdAsync(Guid id);

        /// <summary>
        /// 创建质检记录
        /// </summary>
        /// <param name="createDto">创建质检记录DTO</param>
        /// <returns>创建结果</returns>
        Task<ApiResult<GetWorkReportInspectionDto>> CreateAsync(CreateWorkReportInspectionDto createDto);

        /// <summary>
        /// 更新质检记录
        /// </summary>
        /// <param name="updateDto">更新质检记录DTO</param>
        /// <returns>更新结果</returns>
        Task<ApiResult<GetWorkReportInspectionDto>> UpdateAsync(UpdateWorkReportInspectionDto updateDto);

        /// <summary>
        /// 删除质检记录
        /// </summary>
        /// <param name="id">质检记录Id</param>
        /// <returns>删除结果</returns>
        Task<ApiResult<bool>> DeleteAsync(Guid id);

        /// <summary>
        /// 批量删除质检记录
        /// </summary>
        /// <param name="ids">质检记录Id列表</param>
        /// <returns>删除结果</returns>
        Task<ApiResult<bool>> BatchDeleteAsync(List<Guid> ids);

        /// <summary>
        /// 执行质检操作
        /// </summary>
        /// <param name="id">质检记录Id</param>
        /// <param name="updateDto">质检更新信息</param>
        /// <returns>质检结果</returns>
        Task<ApiResult<GetWorkReportInspectionDto>> PerformInspectionAsync(Guid id, UpdateWorkReportInspectionDto updateDto);

        /// <summary>
        /// 获取质检统计信息
        /// </summary>
        /// <param name="searchDto">搜索条件</param>
        /// <returns>统计信息</returns>
        Task<ApiResult<object>> GetStatisticsAsync(WorkReportInspectionAdvancedSearchDto searchDto);

        /// <summary>
        /// 检查质检相关表的数据情况
        /// </summary>
        /// <returns>各关联表的数据统计</returns>
        Task<ApiResult<object>> CheckRelatedDataAsync();

        /// <summary>
        /// 获取简单质检记录列表（不依赖关联数据）
        /// </summary>
        /// <returns>质检记录基础信息</returns>
        Task<ApiResult<object>> GetSimpleListAsync();

        /// <summary>
        /// 分页获取质检记录列表
        /// </summary>
        /// <param name="inspectionCode">检验单号（可选）</param>
        /// <param name="inspectionName">检验单名称（可选）</param>
        /// <param name="status">状态过滤（可选）</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页数量</param>
        /// <returns>分页的质检记录列表</returns>
        Task<ApiResult<PageResult<List<GetWorkReportInspectionDto>>>> GetPagedListAsync(string? inspectionCode, string? inspectionName, string? status, int pageIndex, int pageSize);
    }
}
