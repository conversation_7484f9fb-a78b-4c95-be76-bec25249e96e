# 质检记录前端API测试脚本
# PowerShell脚本，用于测试新的前端质检API

$baseUrl = "http://localhost:64922"
$headers = @{
    "Content-Type" = "application/json"
}

Write-Host "=== 质检记录前端API测试 ===" -ForegroundColor Green
Write-Host "服务地址: $baseUrl" -ForegroundColor Yellow
Write-Host ""

# 测试1: 获取状态选项
Write-Host "测试1: 获取状态选项" -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/WorkReportInspection/status-options" -Method GET -Headers $headers
    Write-Host "✓ 状态选项获取成功" -ForegroundColor Green
    if ($response.data) {
        Write-Host "可用状态: $($response.data -join ', ')" -ForegroundColor White
    }
} catch {
    Write-Host "✗ 状态选项获取失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试2: 获取质检记录列表 (无查询条件)
Write-Host "测试2: 获取质检记录列表 (无查询条件)" -ForegroundColor Cyan
$body1 = @{
    pageIndex = 1
    pageSize = 5
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/WorkReportInspection/list" -Method POST -Body $body1 -Headers $headers
    Write-Host "✓ 质检记录列表获取成功" -ForegroundColor Green
    if ($response.data) {
        Write-Host "总记录数: $($response.data.totalCount)" -ForegroundColor White
        Write-Host "总页数: $($response.data.totalPage)" -ForegroundColor White
        Write-Host "当前页记录数: $($response.data.data.Count)" -ForegroundColor White
    }
} catch {
    Write-Host "✗ 质检记录列表获取失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试3: 按计划编号查询
Write-Host "测试3: 按计划编号查询" -ForegroundColor Cyan
$body2 = @{
    planNumber = "PLAN"
    pageIndex = 1
    pageSize = 5
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/WorkReportInspection/list" -Method POST -Body $body2 -Headers $headers
    Write-Host "✓ 按计划编号查询成功" -ForegroundColor Green
    if ($response.data) {
        Write-Host "匹配记录数: $($response.data.totalCount)" -ForegroundColor White
    }
} catch {
    Write-Host "✗ 按计划编号查询失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试4: 按产品名称查询
Write-Host "测试4: 按产品名称查询" -ForegroundColor Cyan
$body3 = @{
    productName = "产品"
    pageIndex = 1
    pageSize = 5
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/WorkReportInspection/list" -Method POST -Body $body3 -Headers $headers
    Write-Host "✓ 按产品名称查询成功" -ForegroundColor Green
    if ($response.data) {
        Write-Host "匹配记录数: $($response.data.totalCount)" -ForegroundColor White
    }
} catch {
    Write-Host "✗ 按产品名称查询失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试5: 按状态查询
Write-Host "测试5: 按状态查询" -ForegroundColor Cyan
$body4 = @{
    status = "待质检"
    pageIndex = 1
    pageSize = 5
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/WorkReportInspection/list" -Method POST -Body $body4 -Headers $headers
    Write-Host "✓ 按状态查询成功" -ForegroundColor Green
    if ($response.data) {
        Write-Host "匹配记录数: $($response.data.totalCount)" -ForegroundColor White
    }
} catch {
    Write-Host "✗ 按状态查询失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试6: 组合查询
Write-Host "测试6: 组合查询" -ForegroundColor Cyan
$body5 = @{
    planNumber = "PLAN"
    productName = "产品"
    status = "已质检"
    pageIndex = 1
    pageSize = 5
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/WorkReportInspection/list" -Method POST -Body $body5 -Headers $headers
    Write-Host "✓ 组合查询成功" -ForegroundColor Green
    if ($response.data) {
        Write-Host "匹配记录数: $($response.data.totalCount)" -ForegroundColor White
    }
} catch {
    Write-Host "✗ 组合查询失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

Write-Host "=== 测试完成 ===" -ForegroundColor Green
Write-Host "请查看Swagger文档: $baseUrl/swagger" -ForegroundColor Yellow
