{"info": {"name": "报工质检接口测试 (修改后)", "description": "测试修改后的报工质检接口，支持高级搜索功能", "version": "2.0.0"}, "tests": [{"name": "测试1: 基础列表查询", "method": "POST", "url": "/api/WorkReportInspection/list", "headers": {"Content-Type": "application/json"}, "body": {"pageIndex": 1, "pageSize": 10}, "description": "获取所有质检记录，不使用任何搜索条件"}, {"name": "测试2: 按计划编号搜索", "method": "POST", "url": "/api/WorkReportInspection/list", "headers": {"Content-Type": "application/json"}, "body": {"pageIndex": 1, "pageSize": 10, "planNumber": "PLAN001"}, "description": "根据计划编号搜索质检记录"}, {"name": "测试3: 按产品名称搜索", "method": "POST", "url": "/api/WorkReportInspection/list", "headers": {"Content-Type": "application/json"}, "body": {"pageIndex": 1, "pageSize": 10, "productName": "产品A"}, "description": "根据产品名称搜索质检记录"}, {"name": "测试4: 按状态搜索", "method": "POST", "url": "/api/WorkReportInspection/list", "headers": {"Content-Type": "application/json"}, "body": {"pageIndex": 1, "pageSize": 10, "status": "待质检"}, "description": "根据状态搜索质检记录"}, {"name": "测试5: 组合搜索", "method": "POST", "url": "/api/WorkReportInspection/list", "headers": {"Content-Type": "application/json"}, "body": {"pageIndex": 1, "pageSize": 10, "planNumber": "PLAN001", "productName": "产品A", "status": "待质检"}, "description": "使用多个搜索条件组合查询"}, {"name": "测试6: 关联表字段搜索", "method": "POST", "url": "/api/WorkReportInspection/list", "headers": {"Content-Type": "application/json"}, "body": {"pageIndex": 1, "pageSize": 10, "processStepName": "工序1", "stationName": "站点1", "teamName": "班组1", "reporterName": "张三", "inspectorName": "李四"}, "description": "使用关联表字段进行搜索"}, {"name": "测试7: 时间范围搜索", "method": "POST", "url": "/api/WorkReportInspection/list", "headers": {"Content-Type": "application/json"}, "body": {"pageIndex": 1, "pageSize": 10, "reportTimeStart": "2025-01-01T00:00:00", "reportTimeEnd": "2025-12-31T23:59:59", "inspectionTimeStart": "2025-01-01T00:00:00", "inspectionTimeEnd": "2025-12-31T23:59:59"}, "description": "使用时间范围进行搜索"}, {"name": "测试8: 数量范围搜索", "method": "POST", "url": "/api/WorkReportInspection/list", "headers": {"Content-Type": "application/json"}, "body": {"pageIndex": 1, "pageSize": 10, "minReportedQuantity": 50, "maxReportedQuantity": 200, "minTestedQuantity": 10, "maxTestedQuantity": 100, "minQualificationRate": 0.8, "maxQualificationRate": 1.0}, "description": "使用数量范围进行搜索"}, {"name": "测试9: 工单和任务搜索", "method": "POST", "url": "/api/WorkReportInspection/list", "headers": {"Content-Type": "application/json"}, "body": {"pageIndex": 1, "pageSize": 10, "workOrderName": "工单A", "workOrderCode": "WO001", "taskName": "任务1", "taskCode": "TASK001"}, "description": "使用工单和任务信息进行搜索"}, {"name": "测试10: 获取状态选项", "method": "GET", "url": "/api/WorkReportInspection/status-options", "headers": {"Content-Type": "application/json"}, "description": "获取状态下拉框的选项列表"}, {"name": "测试11: 兼容性测试 - 原有字段", "method": "POST", "url": "/api/WorkReportInspection/list", "headers": {"Content-Type": "application/json"}, "body": {"pageIndex": 1, "pageSize": 10, "inspectionCode": "INS001", "inspectionName": "首检", "inspectionType": "首检", "inspectionDepartment": "质检部", "overallResult": "合格"}, "description": "测试原有搜索字段是否仍然正常工作"}], "expectedResponses": {"successResponse": {"code": 200, "message": "操作成功", "data": {"data": [{"id": "guid", "inspectionCode": "string", "inspectionName": "string", "inspectionType": "string", "status": "string", "productName": "string", "productCode": "string", "planNumber": "string", "planName": "string", "processStepName": "string", "stationName": "string", "teamName": "string", "reporterName": "string", "inspectorName": "string", "workOrderName": "string", "workOrderCode": "string", "taskName": "string", "taskCode": "string", "reportedQuantity": "number", "reportTime": "datetime", "inspectionTime": "datetime", "testedQuantity": "number", "qualifiedQuantity": "number", "unqualifiedQuantity": "number", "overallResult": "string", "remark": "string"}], "totalCount": "number", "totalPage": "number"}}, "statusOptionsResponse": {"code": 200, "message": "操作成功", "data": [{"value": "待质检", "label": "待质检"}, {"value": "质检中", "label": "质检中"}, {"value": "已质检", "label": "已质检"}, {"value": "已完检", "label": "已完检"}, {"value": "不合格", "label": "不合格"}, {"value": "合格", "label": "合格"}]}}, "notes": ["接口已修改为使用高级搜索DTO，支持更全面的搜索功能", "所有新增的搜索字段都是可选的，不会影响现有API调用", "支持关联表字段搜索，包括工序、站点、班组、人员等", "支持时间范围和数量范围搜索", "支持工单和任务信息搜索", "接口保持向后兼容，原有的搜索字段仍然可以正常使用", "计划编号搜索目前使用产品编号进行模糊匹配", "如需精确的计划编号搜索，需要建立质检记录与生产计划的关联关系", "状态值需要与数据库中的实际状态值保持一致"]}