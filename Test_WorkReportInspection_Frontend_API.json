{"info": {"_postman_id": "frontend-workreport-inspection-api", "name": "质检记录API测试 (前端界面专用)", "description": "测试质检记录的前端界面API，支持计划编号、产品名称、状态三个查询条件", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. 获取状态选项", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/WorkReportInspection/status-options", "host": ["{{baseUrl}}"], "path": ["api", "WorkReportInspection", "status-options"]}}, "response": []}, {"name": "2. 质检记录列表 - 无查询条件", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"pageIndex\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/api/WorkReportInspection/list", "host": ["{{baseUrl}}"], "path": ["api", "WorkReportInspection", "list"]}}, "response": []}, {"name": "3. 质检记录列表 - 按计划编号查询", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"planNumber\": \"PLAN001\",\n  \"pageIndex\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/api/WorkReportInspection/list", "host": ["{{baseUrl}}"], "path": ["api", "WorkReportInspection", "list"]}}, "response": []}, {"name": "4. 质检记录列表 - 按产品名称查询", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productName\": \"产品\",\n  \"pageIndex\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/api/WorkReportInspection/list", "host": ["{{baseUrl}}"], "path": ["api", "WorkReportInspection", "list"]}}, "response": []}, {"name": "5. 质检记录列表 - 按状态查询", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"待质检\",\n  \"pageIndex\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/api/WorkReportInspection/list", "host": ["{{baseUrl}}"], "path": ["api", "WorkReportInspection", "list"]}}, "response": []}, {"name": "6. 质检记录列表 - 组合查询", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"planNumber\": \"PLAN\",\n  \"productName\": \"产品\",\n  \"status\": \"已质检\",\n  \"pageIndex\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/api/WorkReportInspection/list", "host": ["{{baseUrl}}"], "path": ["api", "WorkReportInspection", "list"]}}, "response": []}, {"name": "7. 质检记录列表 - 大分页测试", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"pageIndex\": 1,\n  \"pageSize\": 50\n}"}, "url": {"raw": "{{baseUrl}}/api/WorkReportInspection/list", "host": ["{{baseUrl}}"], "path": ["api", "WorkReportInspection", "list"]}}, "response": []}], "variable": [{"key": "baseUrl", "value": "http://localhost:5000", "type": "string"}]}