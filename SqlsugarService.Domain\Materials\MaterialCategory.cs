﻿using SqlSugar;
using SqlsugarService.Domain.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Domain.Materials
{
    /// <summary>
    /// 物料分类 【树形结构】
    /// </summary>
    public class MaterialCategory : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 分类名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 父级分类Id（根节点为null）
        /// </summary>
        public Guid? ParentId { get; set; }
        // 标记 ParentCategory 为忽略字段，不参与插入和更新
        [SqlSugar.SugarColumn(IsIgnore = true)]
        public MaterialCategory? ParentCategory { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 子分类列表
        /// </summary>
        [SqlSugar.SugarColumn(IsIgnore = true)]
        public ICollection<MaterialCategory> ChildCategories { get; set; } = new List<MaterialCategory>();

        /// <summary>
        /// 物料列表
        /// </summary>
        [SqlSugar.SugarColumn(IsIgnore = true)]
        public ICollection<MaterialEntity> Materials { get; set; } = new List<MaterialEntity>();
    }
}
