using System;
using System.ComponentModel.DataAnnotations;

namespace SqlsugarService.Application.DTOs.WorkReportInspectionDto
{
    /// <summary>
    /// 更新报工质检记录DTO
    /// </summary>
    public class UpdateWorkReportInspectionDto
    {
        /// <summary>
        /// 质检记录Id
        /// </summary>
        [Required(ErrorMessage = "质检记录Id不能为空")]
        public Guid Id { get; set; }

        #region 基础信息
        /// <summary>
        /// 检验单号
        /// </summary>
        [StringLength(50, ErrorMessage = "检验单号长度不能超过50个字符")]
        public string? InspectionCode { get; set; }

        /// <summary>
        /// 检验单名称
        /// </summary>
        [StringLength(100, ErrorMessage = "检验单名称长度不能超过100个字符")]
        public string? InspectionName { get; set; }

        /// <summary>
        /// 检验类型 (如：首检, 巡检, 末检)
        /// </summary>
        [StringLength(20, ErrorMessage = "检验类型长度不能超过20个字符")]
        public string? InspectionType { get; set; }

        /// <summary>
        /// 状态 (如: 未质检, 已质检, 已完检)
        /// </summary>
        [StringLength(20, ErrorMessage = "状态长度不能超过20个字符")]
        public string? Status { get; set; }
        #endregion

        #region 关联外键
        /// <summary>
        /// 检验人员Id
        /// </summary>
        public Guid? InspectorId { get; set; }
        #endregion

        #region 质检信息
        /// <summary>
        /// 检验时间
        /// </summary>
        public DateTime? InspectionTime { get; set; }

        /// <summary>
        /// 检验部门
        /// </summary>
        [StringLength(100, ErrorMessage = "检验部门长度不能超过100个字符")]
        public string? InspectionDepartment { get; set; }

        /// <summary>
        /// 检测数量
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "检测数量不能为负数")]
        public int? TestedQuantity { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "合格数量不能为负数")]
        public int? QualifiedQuantity { get; set; }
        
        /// <summary>
        /// 不合格数量
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "不合格数量不能为负数")]
        public int? UnqualifiedQuantity { get; set; }

        /// <summary>
        /// 检测结果 (如: 合格, 不合格)
        /// </summary>
        [StringLength(20, ErrorMessage = "检测结果长度不能超过20个字符")]
        public string? OverallResult { get; set; }
        #endregion
        
        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remark { get; set; }
    }
}
