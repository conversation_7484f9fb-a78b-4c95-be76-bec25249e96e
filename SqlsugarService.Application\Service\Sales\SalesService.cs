﻿using AutoMapper;
using SqlsugarService.Application.DTOs.SalesDto;
using SqlsugarService.Application.IService.Sales;
using SqlsugarService.Application.Until;
using SqlsugarService.Domain.InventoryChange;
using SqlsugarService.Infrastructure.IRepository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.Service.Sales
{
    /// <summary>
    /// 销售订单
    /// </summary>
    public class SalesService : ISalesService
    {
        private readonly IBaseRepository<Salesorder> salesorders;
        private readonly IMapper mapper;

        public SalesService(IBaseRepository<Salesorder> salesorders, IMapper mapper)
        {
            this.salesorders = salesorders;
            this.mapper = mapper;
        }
        /// <summary>
        /// 获取销售订单列表
        /// </summary>
        /// <param name="seach"></param>
        /// <returns></returns>
        public async Task<ApiResult<PageResult<List<getsalesorderDto>>>> GetSalesOrderList(GetsalesorderSearchDto seach)
        {

            var res = await salesorders.GetAllAsync();
            if (!string.IsNullOrEmpty(seach.SalesCode))
            {
                res = res.Where(x => x.SalesCode.Contains(seach.SalesCode)).ToList();
            }
            if (!string.IsNullOrEmpty(seach.SalesName))
            {
                res = res.Where(x => x.SalesName.Contains(seach.SalesName)).ToList();
            }
            var totalCount = res.Count;
            var totalPage = (int)Math.Ceiling(totalCount * 1.0 / seach.PageSize);
            res = res.OrderBy(x => x.Id).Skip((seach.PageIndex - 1) * seach.PageSize).Take(seach.PageSize).ToList();
            // 使用 AutoMapper 将 Salesorder 转换为 getsalesorderDto
            var mappedRes = mapper.Map<List<getsalesorderDto>>(res);
            return ApiResult<PageResult<List<getsalesorderDto>>>.Success(new PageResult<List<getsalesorderDto>>() { TotalCount = totalCount, TotalPage = totalPage, Data = mappedRes }, ResultCode.Success);
        }

        /// <summary>
        /// 新增销售订单
        /// </summary>
        public async Task<ApiResult> AddSalesOrder(insertupdatesalesorderDto salesoutbounddto)
        {
            salesoutbounddto.Id=Guid.NewGuid();
            var salesoutbound = mapper.Map<insertupdatesalesorderDto, Salesorder>(salesoutbounddto);
            var res = await salesorders.InsertAsync(salesoutbound);
            return res ? ApiResult.Success(ResultCode.Success) : ApiResult.Fail("新增失败", ResultCode.Error);
        }
    }
}
