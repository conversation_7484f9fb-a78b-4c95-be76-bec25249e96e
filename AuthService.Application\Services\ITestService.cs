using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace AuthService.Application.Services;

/// <summary>
/// 测试服务接口 - 用于验证自动发现功能
/// </summary>
public interface ITestService
{
    /// <summary>
    /// 获取测试消息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>测试消息</returns>
    Task<string> GetMessageAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据ID获取测试数据
    /// </summary>
    /// <param name="id">ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>测试数据</returns>
    Task<TestData> GetTestDataAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 创建测试数据
    /// </summary>
    /// <param name="name">名称</param>
    /// <param name="value">值</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的测试数据</returns>
    Task<TestData> CreateTestDataAsync(string name, int value, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有测试数据
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="size">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>测试数据列表</returns>
    Task<List<TestData>> GetAllTestDataAsync(int page = 1, int size = 10, CancellationToken cancellationToken = default);
}

/// <summary>
/// 测试数据模型
/// </summary>
public class TestData
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int Value { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
