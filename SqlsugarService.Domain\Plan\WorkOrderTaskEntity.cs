using SqlSugar;
using SqlsugarService.Domain.Common;
using SqlsugarService.Domain.Craftsmanship;
using System;

namespace SqlsugarService.Domain.Plan
{
    /// <summary>
    /// 工单任务表
    /// </summary>
    [SugarTable("work_order_task")]
    public class WorkOrderTaskEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 序号
        /// </summary>
        public int SequenceNumber { get; set; }

        /// <summary>
        /// 任务编号
        /// </summary>
        public string TaskNumber { get; set; } = string.Empty;

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; } = string.Empty;

        /// <summary>
        /// 关联的生产工单ID
        /// </summary>
        public Guid ProductionOrderId { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        public string StationName { get; set; } = string.Empty;

        /// <summary>
        /// 工艺编号
        /// </summary>
        public string ProcessCode { get; set; } = string.Empty;

        /// <summary>
        /// 工艺名称
        /// </summary>
        public string ProcessName { get; set; } = string.Empty;

        /// <summary>
        /// 工艺流程
        /// </summary>
        public string ProcessFlow { get; set; } = string.Empty;

        /// <summary>
        /// 工艺类型
        /// </summary>
        public string ProcessType { get; set; } = string.Empty;

        /// <summary>
        /// 任务颜色（用于界面显示）
        /// </summary>
        public string TaskColor { get; set; } = string.Empty;

        /// <summary>
        /// 计划数量
        /// </summary>
        public decimal PlanQuantity { get; set; }

        /// <summary>
        /// 实际生产数量
        /// </summary>
        public decimal ActualQuantity { get; set; } = 0;

        /// <summary>
        /// 计划开工时间
        /// </summary>
        public DateTime PlanStartTime { get; set; }

        /// <summary>
        /// 计划完工时间
        /// </summary>
        public DateTime PlanEndTime { get; set; }

        /// <summary>
        /// 实际开工时间
        /// </summary>
        public DateTime? ActualStartTime { get; set; }

        /// <summary>
        /// 实际完工时间
        /// </summary>
        public DateTime? ActualEndTime { get; set; }

        /// <summary>
        /// 计划用时（小时）
        /// </summary>
        public decimal PlanDuration { get; set; }

        /// <summary>
        /// 实际用时（小时）
        /// </summary>
        public decimal? ActualDuration { get; set; }

        /// <summary>
        /// 任务状态（未开工、进行中、已完成、已暂停、已取消等）
        /// </summary>
        public string Status { get; set; } = "未开工";

        /// <summary>
        /// 工艺路线ID
        /// </summary>
        public Guid? ProcessRouteId { get; set; }

        /// <summary>
        /// 工序ID
        /// </summary>
        public Guid? ProcessStepId { get; set; }

        /// <summary>
        /// 优先级（1-低，2-中，3-高，4-紧急）
        /// </summary>
        public int Priority { get; set; } = 2;

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 导航属性 - 生产工单
        /// </summary>
        [Navigate(NavigateType.ManyToOne, nameof(ProductionOrderId))]
        public ProductionOrder? ProductionOrder { get; set; }

        /// <summary>
        /// 导航属性 - 工艺路线
        /// </summary>
        [Navigate(NavigateType.ManyToOne, nameof(ProcessRouteId))]
        public ProcessRouteEntity? ProcessRoute { get; set; }

        /// <summary>
        /// 导航属性 - 工序
        /// </summary>
        [Navigate(NavigateType.ManyToOne, nameof(ProcessStepId))]
        public ProcessStep? ProcessStep { get; set; }
    }
}