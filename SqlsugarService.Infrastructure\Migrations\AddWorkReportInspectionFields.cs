using SqlSugar;
using SqlsugarService.Domain.QualityInspection;

namespace SqlsugarService.Infrastructure.Migrations
{
    /// <summary>
    /// 添加质检表缺失字段的迁移
    /// </summary>
    public class AddWorkReportInspectionFields
    {
        private readonly ISqlSugarClient _db;

        public AddWorkReportInspectionFields(ISqlSugarClient db)
        {
            _db = db;
        }

        /// <summary>
        /// 执行迁移 - 添加字段
        /// </summary>
        public async Task UpAsync()
        {
            try
            {
                // 检查表是否存在
                var tableExists = _db.DbMaintenance.IsAnyTable("workreportinspectionentity", false);
                if (!tableExists)
                {
                    // 如果表不存在，直接创建完整的表
                    _db.CodeFirst.InitTables<WorkReportInspectionEntity>();
                    Console.WriteLine("质检表不存在，已创建完整表结构");
                    return;
                }

                // 检查字段是否已存在
                var columns = _db.DbMaintenance.GetColumnInfosByTableName("workreportinspectionentity", false);
                var hasProductionPlanId = columns.Any(c => c.DbColumnName.ToLower() == "productionplanid");
                var hasProductionOrderId = columns.Any(c => c.DbColumnName.ToLower() == "productionorderid");

                // 添加生产计划ID字段
                if (!hasProductionPlanId)
                {
                    await _db.Ado.ExecuteCommandAsync(@"
                        ALTER TABLE workreportinspectionentity 
                        ADD COLUMN productionplanid UUID NULL");
                    
                    await _db.Ado.ExecuteCommandAsync(@"
                        COMMENT ON COLUMN workreportinspectionentity.productionplanid IS '生产计划Id'");
                    
                    Console.WriteLine("已添加 productionplanid 字段");
                }
                else
                {
                    Console.WriteLine("productionplanid 字段已存在，跳过");
                }

                // 添加生产工单ID字段
                if (!hasProductionOrderId)
                {
                    await _db.Ado.ExecuteCommandAsync(@"
                        ALTER TABLE workreportinspectionentity 
                        ADD COLUMN productionorderid UUID NULL");
                    
                    await _db.Ado.ExecuteCommandAsync(@"
                        COMMENT ON COLUMN workreportinspectionentity.productionorderid IS '工单Id (生产工单)'");
                    
                    Console.WriteLine("已添加 productionorderid 字段");
                }
                else
                {
                    Console.WriteLine("productionorderid 字段已存在，跳过");
                }

                Console.WriteLine("质检表字段迁移完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"迁移执行失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 回滚迁移 - 删除字段
        /// </summary>
        public async Task DownAsync()
        {
            try
            {
                // 删除生产计划ID字段
                await _db.Ado.ExecuteCommandAsync(@"
                    ALTER TABLE workreportinspectionentity 
                    DROP COLUMN IF EXISTS productionplanid");

                // 删除生产工单ID字段
                await _db.Ado.ExecuteCommandAsync(@"
                    ALTER TABLE workreportinspectionentity 
                    DROP COLUMN IF EXISTS productionorderid");

                Console.WriteLine("质检表字段回滚完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"回滚执行失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 验证迁移结果
        /// </summary>
        public async Task<bool> ValidateAsync()
        {
            try
            {
                var columns = _db.DbMaintenance.GetColumnInfosByTableName("workreportinspectionentity", false);
                var hasProductionPlanId = columns.Any(c => c.DbColumnName.ToLower() == "productionplanid");
                var hasProductionOrderId = columns.Any(c => c.DbColumnName.ToLower() == "productionorderid");

                var isValid = hasProductionPlanId && hasProductionOrderId;
                
                Console.WriteLine($"迁移验证结果: {(isValid ? "成功" : "失败")}");
                Console.WriteLine($"- productionplanid 字段: {(hasProductionPlanId ? "存在" : "不存在")}");
                Console.WriteLine($"- productionorderid 字段: {(hasProductionOrderId ? "存在" : "不存在")}");

                return isValid;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"验证失败: {ex.Message}");
                return false;
            }
        }
    }
}