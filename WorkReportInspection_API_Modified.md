# 报工质检接口修改完成 ✅

## 📋 **修改概述**

已将质检的第一张效果图接口修改成第二张效果图接口查询的形式，现在 `/api/WorkReportInspection/list` 接口支持高级搜索功能：

- **计划编号** (PlanNumber) - 对应前端"计划编号"输入框
- **产品名称** (ProductName) - 对应前端"产品名称"输入框
- **状态** (Status) - 对应前端"状态"下拉选择框
- **关联表字段搜索** - 支持工序、站点、班组、人员等关联信息搜索
- **时间范围搜索** - 支持报工时间和质检时间范围查询
- **数量范围搜索** - 支持报工数量、检测数量、合格率范围查询

## 🔧 **已完成的修改**

### 1. 控制器修改

- ✅ **WorkReportInspectionController** - 将 `/api/WorkReportInspection/list` 接口改为使用高级搜索 DTO
- ✅ 接口现在使用 `WorkReportInspectionAdvancedSearchDto` 替代 `GetWorkReportInspectionSearchDto`

### 2. 功能增强

- ✅ 支持更全面的关联表字段搜索
- ✅ 支持时间范围和数量范围搜索
- ✅ 保持向后兼容，原有搜索字段仍然可用

## 🔍 **修改后的搜索接口**

### POST `/api/WorkReportInspection/list`

**请求体示例：**

```json
{
  "pageIndex": 1,
  "pageSize": 10,
  "planNumber": "PLAN001",
  "productName": "产品A",
  "status": "待质检",
  "inspectionCode": "",
  "inspectionName": "",
  "inspectionType": "",
  "inspectionDepartment": "",
  "overallResult": "",
  "processStepName": "",
  "stationName": "",
  "teamName": "",
  "reporterName": "",
  "inspectorName": "",
  "workOrderName": "",
  "workOrderCode": "",
  "taskName": "",
  "taskCode": "",
  "reportTimeStart": "2025-01-01T00:00:00",
  "reportTimeEnd": "2025-12-31T23:59:59",
  "inspectionTimeStart": null,
  "inspectionTimeEnd": null,
  "minReportedQuantity": 0,
  "maxReportedQuantity": 1000,
  "minTestedQuantity": 0,
  "maxTestedQuantity": 1000,
  "minQualificationRate": 0.8,
  "maxQualificationRate": 1.0
}
```

**响应示例：**

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "data": [
      {
        "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "inspectionCode": "INS001",
        "inspectionName": "首检记录",
        "inspectionType": "首检",
        "status": "待质检",
        "productName": "产品A",
        "productCode": "PROD001",
        "planNumber": "PLAN001",
        "planName": "生产计划A",
        "processStepName": "工序1",
        "stationName": "站点1",
        "teamName": "班组1",
        "reporterName": "张三",
        "inspectorName": "李四",
        "workOrderName": "工单A",
        "workOrderCode": "WO001",
        "taskName": "任务1",
        "taskCode": "TASK001",
        "reportedQuantity": 100,
        "reportTime": "2025-07-28T12:00:00",
        "inspectionTime": null,
        "testedQuantity": null,
        "qualifiedQuantity": null,
        "unqualifiedQuantity": null,
        "overallResult": null,
        "remark": null
      }
    ],
    "totalCount": 1,
    "totalPage": 1
  }
}
```

## 📊 **新增搜索字段说明**

### 基础搜索字段

- `inspectionCode` - 检验单号
- `inspectionName` - 检验单名称
- `inspectionType` - 检验类型
- `status` - 状态
- `inspectionDepartment` - 检验部门
- `overallResult` - 检测结果

### 关联表搜索字段

- `productName` - 产品名称
- `productCode` - 产品编号
- `processStepName` - 工序名称
- `stationName` - 站点名称
- `teamName` - 班组名称
- `reporterName` - 报工人员姓名
- `inspectorName` - 检验人员姓名
- `workOrderName` - 工单名称
- `workOrderCode` - 工单编号
- `taskName` - 任务名称
- `taskCode` - 任务编号

### 时间范围搜索

- `reportTimeStart` - 报工开始时间
- `reportTimeEnd` - 报工结束时间
- `inspectionTimeStart` - 检验开始时间
- `inspectionTimeEnd` - 检验结束时间

### 数量范围搜索

- `minReportedQuantity` - 最小报工数量
- `maxReportedQuantity` - 最大报工数量
- `minTestedQuantity` - 最小检测数量
- `maxTestedQuantity` - 最大检测数量
- `minQualificationRate` - 最小合格率
- `maxQualificationRate` - 最大合格率

## 🎯 **前端集成说明**

### 1. 搜索表单绑定

```javascript
// 高级搜索条件对象
const searchForm = {
  pageIndex: 1,
  pageSize: 10,
  planNumber: "", // 计划编号输入框
  productName: "", // 产品名称输入框
  status: "", // 状态下拉框
  inspectionCode: "", // 检验单号
  inspectionName: "", // 检验单名称
  inspectionType: "", // 检验类型
  inspectionDepartment: "", // 检验部门
  overallResult: "", // 检测结果
  processStepName: "", // 工序名称
  stationName: "", // 站点名称
  teamName: "", // 班组名称
  reporterName: "", // 报工人员
  inspectorName: "", // 检验人员
  workOrderName: "", // 工单名称
  workOrderCode: "", // 工单编号
  taskName: "", // 任务名称
  taskCode: "", // 任务编号
  reportTimeStart: null, // 报工开始时间
  reportTimeEnd: null, // 报工结束时间
  inspectionTimeStart: null, // 检验开始时间
  inspectionTimeEnd: null, // 检验结束时间
  minReportedQuantity: null, // 最小报工数量
  maxReportedQuantity: null, // 最大报工数量
  minTestedQuantity: null, // 最小检测数量
  maxTestedQuantity: null, // 最大检测数量
  minQualificationRate: null, // 最小合格率
  maxQualificationRate: null, // 最大合格率
};

// 搜索方法
const handleSearch = async () => {
  const response = await fetch("/api/WorkReportInspection/list", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(searchForm),
  });
  const result = await response.json();
  // 处理返回的数据...
};
```

### 2. 兼容性说明

- 原有的基础搜索字段仍然可以使用
- 新增的搜索字段都是可选的
- 不会影响现有的 API 调用

## 🔧 **技术实现说明**

### 修改内容：

1. **WorkReportInspectionController** - 将基础列表接口改为使用高级搜索 DTO
2. **接口参数** - 从 `GetWorkReportInspectionSearchDto` 改为 `WorkReportInspectionAdvancedSearchDto`
3. **服务调用** - 从 `GetListAsync` 改为 `GetAdvancedListAsync`

### 兼容性：

- 保留了所有原有搜索字段，确保现有功能不受影响
- 新增字段为可选参数，不会破坏现有 API 调用
- 支持更全面的关联表搜索功能

## 📝 **注意事项**

1. 接口现在支持更全面的搜索功能，包括关联表字段搜索
2. 时间范围搜索支持精确的时间范围查询
3. 数量范围搜索支持数值范围查询
4. 所有搜索字段都是可选的，可以根据需要组合使用
5. 接口保持向后兼容，原有的搜索方式仍然有效
