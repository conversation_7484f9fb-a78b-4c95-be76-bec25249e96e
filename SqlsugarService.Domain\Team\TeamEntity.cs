using SqlSugar;
using SqlsugarService.Domain.Common;
using System;

namespace SqlsugarService.Domain.Team
{
    /// <summary>
    /// 班组实体类
    /// </summary>
    public class TeamEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 班组名称
        /// </summary>
        public string TeamName { get; set; }

        /// <summary>
        /// 班组编码
        /// </summary>
        public string TeamCode { get; set; }

        /// <summary>
        /// 班组类型 (如: 机加, 装配)
        /// </summary>
        public string TeamType { get; set; }

        /// <summary>
        /// 负责人Id
        /// </summary>
        public Guid LeaderId { get; set; }

        /// <summary>
        /// 班组成员数量
        /// </summary>
        public int MemberCount { get; set; }

        /// <summary>
        /// 状态 (如: 启用, 禁用)
        /// </summary>
        public string Status { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
} 