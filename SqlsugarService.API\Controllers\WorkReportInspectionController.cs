using Microsoft.AspNetCore.Mvc;
using SqlsugarService.Application.DTOs.WorkReportInspectionDto;
using SqlsugarService.Application.IService.WorkReportInspection;
using SqlsugarService.Application.Until;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SqlsugarService.API.Controllers
{
    /// <summary>
    /// 报工质检控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class WorkReportInspectionController : ControllerBase
    {
        private readonly IWorkReportInspectionService _workReportInspectionService;

        public WorkReportInspectionController(IWorkReportInspectionService workReportInspectionService)
        {
            _workReportInspectionService = workReportInspectionService;
        }

        /// <summary>
        /// 分页获取质检记录列表 (高级搜索)
        /// 功能：根据条件查询质检记录列表，支持分页显示和多条件过滤，包括关联表字段搜索
        /// </summary>
        /// <param name="searchDto">高级搜索条件DTO</param>
        /// <returns>分页的质检记录列表</returns>
        [HttpPost("list")]
        public async Task<ApiResult<PageResult<List<GetWorkReportInspectionDto>>>> GetWorkReportInspectionList(
            [FromBody] WorkReportInspectionAdvancedSearchDto searchDto)
        {
            try
            {
                // 参数验证：检查分页参数的合理性
                if (searchDto.PageIndex < 1)
                {
                    return ApiResult<PageResult<List<GetWorkReportInspectionDto>>>.Fail(
                        "页码必须大于0", ResultCode.ValidationError);
                }

                if (searchDto.PageSize < 1 || searchDto.PageSize > 1000)
                {
                    return ApiResult<PageResult<List<GetWorkReportInspectionDto>>>.Fail(
                        "每页大小必须在1-1000之间", ResultCode.ValidationError);
                }

                // 调用业务服务层处理高级搜索逻辑
                return await _workReportInspectionService.GetAdvancedListAsync(searchDto);
            }
            catch (Exception ex)
            {
                // 捕获并处理所有未预期的异常，返回统一的错误格式
                return ApiResult<PageResult<List<GetWorkReportInspectionDto>>>.Fail(
                    $"获取质检记录列表失败: {ex.Message}", 
                    ResultCode.Error);
            }
        }

        /// <summary>
        /// 分页获取质检记录列表 (简单搜索，保持向后兼容)
        /// </summary>
        /// <param name="inspectionCode">检验单号（可选），支持模糊查询</param>
        /// <param name="inspectionName">检验单名称（可选），支持模糊查询</param>
        /// <param name="status">状态过滤（可选），为空时获取所有状态的质检记录</param>
        /// <param name="pageIndex">页码，从1开始，默认为1</param>
        /// <param name="pageSize">每页数量，默认为10，最大不超过1000</param>
        /// <returns>分页的质检记录列表</returns>
        [HttpGet("simple-list-paged")]
        public async Task<ApiResult<PageResult<List<GetWorkReportInspectionDto>>>> GetWorkReportInspectionPaged(
            [FromQuery] string? inspectionCode,
            [FromQuery] string? inspectionName,
            [FromQuery] string? status,
            [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                // 参数验证：检查分页参数的合理性
                if (pageIndex < 1)
                {
                    return ApiResult<PageResult<List<GetWorkReportInspectionDto>>>.Fail(
                        "页码必须大于0", ResultCode.ValidationError);
                }

                if (pageSize < 1 || pageSize > 1000)
                {
                    return ApiResult<PageResult<List<GetWorkReportInspectionDto>>>.Fail(
                        "每页大小必须在1-1000之间", ResultCode.ValidationError);
                }

                // 调用业务服务层处理分页查询逻辑
                return await _workReportInspectionService.GetPagedListAsync(inspectionCode, inspectionName, status, pageIndex, pageSize);
            }
            catch (Exception ex)
            {
                // 捕获并处理所有未预期的异常，返回统一的错误格式
                return ApiResult<PageResult<List<GetWorkReportInspectionDto>>>.Fail(
                    $"获取质检记录列表失败: {ex.Message}", 
                    ResultCode.Error);
            }
        }



        /// <summary>
        /// 根据Id获取质检记录详情
        /// </summary>
        /// <param name="id">质检记录Id</param>
        /// <returns>质检记录详情</returns>
        [HttpGet("{id}")]
        public async Task<ApiResult<GetWorkReportInspectionDto>> GetById(Guid id)
        {
            return await _workReportInspectionService.GetByIdAsync(id);
        }

        /// <summary>
        /// 创建质检记录
        /// </summary>
        /// <param name="createDto">创建质检记录DTO</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<ApiResult<GetWorkReportInspectionDto>> Create([FromBody] CreateWorkReportInspectionDto createDto)
        {
            return await _workReportInspectionService.CreateAsync(createDto);
        }

        /// <summary>
        /// 更新质检记录
        /// </summary>
        /// <param name="updateDto">更新质检记录DTO</param>
        /// <returns>更新结果</returns>
        [HttpPut]
        public async Task<ApiResult<GetWorkReportInspectionDto>> Update([FromBody] UpdateWorkReportInspectionDto updateDto)
        {
            return await _workReportInspectionService.UpdateAsync(updateDto);
        }

        /// <summary>
        /// 删除质检记录
        /// </summary>
        /// <param name="id">质检记录Id</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<ApiResult<bool>> Delete(Guid id)
        {
            return await _workReportInspectionService.DeleteAsync(id);
        }

        /// <summary>
        /// 批量删除质检记录
        /// </summary>
        /// <param name="ids">质检记录Id列表</param>
        /// <returns>删除结果</returns>
        [HttpPost("batch-delete")]
        public async Task<ApiResult<bool>> BatchDelete([FromBody] List<Guid> ids)
        {
            return await _workReportInspectionService.BatchDeleteAsync(ids);
        }

        /// <summary>
        /// 执行质检操作
        /// </summary>
        /// <param name="id">质检记录Id</param>
        /// <param name="updateDto">质检更新信息</param>
        /// <returns>质检结果</returns>
        [HttpPost("{id}/perform-inspection")]
        public async Task<ApiResult<GetWorkReportInspectionDto>> PerformInspection(Guid id, [FromBody] UpdateWorkReportInspectionDto updateDto)
        {
            return await _workReportInspectionService.PerformInspectionAsync(id, updateDto);
        }

        /// <summary>
        /// 获取质检统计信息
        /// </summary>
        /// <param name="searchDto">搜索条件</param>
        /// <returns>统计信息</returns>
        [HttpPost("statistics")]
        public async Task<ApiResult<object>> GetStatistics([FromBody] WorkReportInspectionAdvancedSearchDto searchDto)
        {
            return await _workReportInspectionService.GetStatisticsAsync(searchDto);
        }

        /// <summary>
        /// 获取检验类型选项
        /// </summary>
        /// <returns>检验类型列表</returns>
        [HttpGet("inspection-types")]
        public ApiResult<List<string>> GetInspectionTypes()
        {
            var inspectionTypes = new List<string>
            {
                "首检",
                "巡检",
                "末检",
                "自检",
                "专检",
                "抽检"
            };
            return ApiResult<List<string>>.Success(inspectionTypes, ResultCode.Success);
        }

        /// <summary>
        /// 获取质检状态选项 (用于前端状态下拉框)
        /// </summary>
        /// <returns>状态列表</returns>
        [HttpGet("status-options")]
        public ApiResult<List<object>> GetStatusOptions()
        {
            var statusOptions = new List<object>
            {
                new { value = "", label = "全部状态" },
                new { value = "待质检", label = "待质检" },
                new { value = "质检中", label = "质检中" },
                new { value = "已质检", label = "已质检" },
                new { value = "已完检", label = "已完检" },
                new { value = "不合格", label = "不合格" },
                new { value = "合格", label = "合格" }
            };
            return ApiResult<List<object>>.Success(statusOptions, ResultCode.Success);
        }



        /// <summary>
        /// 获取检测结果选项
        /// </summary>
        /// <returns>检测结果列表</returns>
        [HttpGet("result-options")]
        public ApiResult<List<string>> GetResultOptions()
        {
            var resultOptions = new List<string>
            {
                "合格",
                "不合格"
            };
            return ApiResult<List<string>>.Success(resultOptions, ResultCode.Success);
        }

        /// <summary>
        /// 检查质检相关表的数据情况
        /// </summary>
        /// <returns>各关联表的数据统计</returns>
        [HttpGet("check-related-data")]
        public async Task<ApiResult<object>> CheckRelatedData()
        {
            try
            {
                return await _workReportInspectionService.CheckRelatedDataAsync();
            }
            catch (Exception ex)
            {
                return ApiResult<object>.Fail($"检查关联数据失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 获取简单质检记录列表（不依赖关联数据）
        /// </summary>
        /// <returns>质检记录基础信息</returns>
        [HttpGet("simple-list")]
        public async Task<ApiResult<object>> GetSimpleList()
        {
            try
            {
                return await _workReportInspectionService.GetSimpleListAsync();
            }
            catch (Exception ex)
            {
                return ApiResult<object>.Fail($"获取简单列表失败: {ex.Message}", ResultCode.Error);
            }
        }
    }
}
