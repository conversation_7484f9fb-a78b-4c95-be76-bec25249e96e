using SqlSugar;
using SqlsugarService.Domain.Common;
using System;

namespace SqlsugarService.Domain.QualityInspection
{
    /// <summary>
    /// 质检方案实体类
    /// </summary>
    public class QualityInspectionPlanEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 质检方案名称
        /// </summary>
        public string PlanName { get; set; }

        /// <summary>
        /// 质检方案编号
        /// </summary>
        public string PlanCode { get; set; }

        /// <summary>
        /// 检测分类 (如：产品, 物料)
        /// </summary>
        public string DetectionCategory { get; set; }

        /// <summary>
        /// 检测种类 (多个以逗号分隔, 如: 首检,自检,中检)
        /// </summary>
        public string DetectionTypes { get; set; }

        /// <summary>
        /// 状态 (如: 启用, 禁用)
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
} 