# 简单的数据库迁移脚本
$env:PGPASSWORD = "kong"

Write-Host "执行数据库迁移..." -ForegroundColor Green

# 执行SQL命令
$sql = @"
ALTER TABLE workreportinspectionentity ADD COLUMN IF NOT EXISTS productionplanid UUID NULL;
ALTER TABLE workreportinspectionentity ADD COLUMN IF NOT EXISTS productionorderid UUID NULL;
COMMENT ON COLUMN workreportinspectionentity.productionplanid IS '生产计划Id';
COMMENT ON COLUMN workreportinspectionentity.productionorderid IS '工单Id (生产工单)';
SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_name = 'workreportinspectionentity' AND column_name IN ('productionplanid', 'productionorderid') ORDER BY column_name;
"@

$sql | psql -h ************* -p 5432 -U kong -d sqlsugardata

Write-Host "迁移完成!" -ForegroundColor Green

Remove-Item Env:PGPASSWORD