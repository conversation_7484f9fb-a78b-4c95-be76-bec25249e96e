-- 质检表添加缺失字段的迁移脚本
-- 数据库: PostgreSQL
-- 表名: workreportinspectionentity

-- 添加生产计划ID字段
ALTER TABLE workreportinspectionentity 
ADD COLUMN IF NOT EXISTS productionplanid UUID NULL;

-- 添加生产工单ID字段  
ALTER TABLE workreportinspectionentity 
ADD COLUMN IF NOT EXISTS productionorderid UUID NULL;

-- 添加字段注释
COMMENT ON COLUMN workreportinspectionentity.productionplanid IS '生产计划Id';
COMMENT ON COLUMN workreportinspectionentity.productionorderid IS '工单Id (生产工单)';

-- 验证字段是否添加成功
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'workreportinspectionentity' 
  AND column_name IN ('productionplanid', 'productionorderid')
ORDER BY column_name;