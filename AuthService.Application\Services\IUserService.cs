using AuthService.Domain.Entities;

namespace AuthService.Application.Services;

/// <summary>
/// 用户服务接口（简化版）
/// 定义用户管理的核心业务逻辑
/// </summary>
public interface IUserService
{
    /// <summary>
    /// 获取分页用户列表
    /// </summary>
    /// <param name="page">页码，从1开始</param>
    /// <param name="size">每页大小</param>
    /// <param name="tenantId">租户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户列表和总数</returns>
    Task<(IEnumerable<User> Users, int TotalCount)> GetUsersAsync(
        int page = 1,
        int size = 10,
        string? tenantId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据ID获取用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户信息</returns>
    Task<User?> GetUserByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据用户名获取用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户信息</returns>
    Task<User?> GetUserByUsernameAsync(string username, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据邮箱获取用户
    /// </summary>
    /// <param name="email">邮箱</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户信息</returns>
    Task<User?> GetUserByEmailAsync(string email, CancellationToken cancellationToken = default);

    /// <summary>
    /// 创建新用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="email">邮箱</param>
    /// <param name="password">密码</param>
    /// <param name="displayName">显示名称</param>
    /// <param name="tenantId">租户ID</param>
    /// <param name="roles">用户角色</param>
    /// <param name="createdBy">创建者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的用户</returns>
    Task<User> CreateUserAsync(
        string username,
        string email,
        string password,
        string? displayName = null,
        string? tenantId = null,
        IEnumerable<string>? roles = null,
        string? createdBy = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新用户信息
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="displayName">显示名称</param>
    /// <param name="email">邮箱</param>
    /// <param name="isActive">是否激活</param>
    /// <param name="roles">用户角色</param>
    /// <param name="updatedBy">更新者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新的用户</returns>
    Task<User?> UpdateUserAsync(
        Guid id,
        string? displayName = null,
        string? email = null,
        bool? isActive = null,
        IEnumerable<string>? roles = null,
        string? updatedBy = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除用户（软删除）
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="deletedBy">删除者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteUserAsync(Guid id, string? deletedBy = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证用户密码
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果和用户信息</returns>
    Task<(bool IsValid, User? User)> ValidateUserAsync(string username, string password, CancellationToken cancellationToken = default);

    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="loginRequest">登录请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>登录结果</returns>
    Task<LoginResult> LoginAsync(LoginRequest loginRequest, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更改用户密码
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="oldPassword">旧密码</param>
    /// <param name="newPassword">新密码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否更改成功</returns>
    Task<bool> ChangePasswordAsync(Guid id, string oldPassword, string newPassword, CancellationToken cancellationToken = default);

    /// <summary>
    /// 重置用户密码
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="newPassword">新密码</param>
    /// <param name="resetBy">重置者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否重置成功</returns>
    Task<bool> ResetPasswordAsync(Guid id, string newPassword, string? resetBy = null, CancellationToken cancellationToken = default);



    /// <summary>
    /// 搜索用户
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="page">页码</param>
    /// <param name="size">每页大小</param>
    /// <param name="tenantId">租户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>搜索结果</returns>
    Task<(IEnumerable<User> Users, int TotalCount)> SearchUsersAsync(
        string keyword,
        int page = 1,
        int size = 10,
        string? tenantId = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 登录请求
/// </summary>
public class LoginRequest
{
    /// <summary>
    /// 用户名或邮箱
    /// </summary>
    public string UsernameOrEmail { get; set; } = string.Empty;

    /// <summary>
    /// 密码
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// 是否记住登录状态
    /// </summary>
    public bool RememberMe { get; set; } = false;

    /// <summary>
    /// 客户端IP地址
    /// </summary>
    public string? ClientIp { get; set; }

    /// <summary>
    /// 用户代理信息
    /// </summary>
    public string? UserAgent { get; set; }

    /// <summary>
    /// 租户ID
    /// </summary>
    public string? TenantId { get; set; }
}

/// <summary>
/// 登录结果
/// </summary>
public class LoginResult
{
    /// <summary>
    /// 是否登录成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// 用户信息
    /// </summary>
    public User? User { get; set; }

    /// <summary>
    /// 访问令牌（预留，后续实现JWT）
    /// </summary>
    public string? AccessToken { get; set; }

    /// <summary>
    /// 刷新令牌（预留，后续实现双token）
    /// </summary>
    public string? RefreshToken { get; set; }

    /// <summary>
    /// 令牌过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 登录时间
    /// </summary>
    public DateTime LoginTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 会话ID（预留）
    /// </summary>
    public string? SessionId { get; set; }

    /// <summary>
    /// 用户权限信息
    /// </summary>
    public UserPermissionInfo? Permissions { get; set; }
}

/// <summary>
/// 用户权限信息
/// </summary>
public class UserPermissionInfo
{
    /// <summary>
    /// 用户角色
    /// </summary>
    public List<string> Roles { get; set; } = new();

    /// <summary>
    /// 用户权限
    /// </summary>
    public List<string> Permissions { get; set; } = new();

    /// <summary>
    /// 租户ID
    /// </summary>
    public string? TenantId { get; set; }

    /// <summary>
    /// 是否为超级管理员
    /// </summary>
    public bool IsSuperAdmin { get; set; }
}

// UserStatistics 类已在 AuthService.Domain.Repositories 中定义
