# 📋 API迁移指南

## 🎯 概述

本项目已从静态控制器迁移到动态API系统。旧的API端点已标记为废弃，建议尽快迁移到新的动态API端点。

## ⚠️ 废弃的API端点

### 用户管理API (UserController)
```
❌ 已废弃：/api/user/*
✅ 新端点：/api/dynamic/users/*
```

### 动态API管理 (DynamicApiController)
```
❌ 已废弃：/api/v1/DynamicApi/*
✅ 新端点：/api/DynamicApiManagement/*
```

## 🔄 API端点映射对照表

### 用户管理API迁移

| 旧端点 (废弃) | 新端点 (推荐) | 说明 |
|--------------|--------------|------|
| `GET /api/user?page=1&size=10` | `GET /api/dynamic/users/users?page=1&size=10` | 获取用户列表 |
| `GET /api/user/{id}` | `GET /api/dynamic/users/userbyid/{id}` | 根据ID获取用户 |
| `POST /api/user` | `POST /api/dynamic/users/user` | 创建用户 |
| `PUT /api/user/{id}` | `PUT /api/dynamic/users/user/{id}` | 更新用户 |
| `DELETE /api/user/{id}` | `DELETE /api/dynamic/users/user/{id}` | 删除用户 |
| `GET /api/user/search?keyword=xxx` | `GET /api/dynamic/users/searchusers?keyword=xxx` | 搜索用户 |

### 动态API管理迁移

| 旧端点 (废弃) | 新端点 (推荐) | 说明 |
|--------------|--------------|------|
| `GET /api/v1/DynamicApi/endpoints` | `GET /api/DynamicApiManagement/endpoints` | 获取所有端点 |
| `POST /api/v1/DynamicApi/refresh` | `POST /api/DynamicApiManagement/refresh` | 刷新映射 |
| `PUT /api/v1/DynamicApi/{id}/toggle` | `PUT /api/DynamicApiManagement/endpoints/{id}/toggle` | 启用/禁用端点 |

## 🚀 新增功能

### 自动生成的用户API端点

动态API系统为UserService自动生成了以下端点：

```bash
# 基础CRUD操作
GET    /api/dynamic/users/users                    # 获取用户列表
GET    /api/dynamic/users/userbyid/{id}            # 根据ID获取用户
GET    /api/dynamic/users/userbyusername           # 根据用户名获取用户
GET    /api/dynamic/users/userbyemail              # 根据邮箱获取用户
POST   /api/dynamic/users/user                     # 创建用户
PUT    /api/dynamic/users/user/{id}                # 更新用户
DELETE /api/dynamic/users/user/{id}                # 删除用户

# 认证相关
POST   /api/dynamic/users/validateuser             # 验证用户密码
PUT    /api/dynamic/users/changepassword/{id}      # 更改密码
PUT    /api/dynamic/users/resetpassword/{id}       # 重置密码

# 查询和统计
GET    /api/dynamic/users/userstatistics           # 获取用户统计
GET    /api/dynamic/users/searchusers              # 搜索用户
```

### 动态API管理端点

```bash
# 端点管理
GET    /api/DynamicApiManagement/endpoints         # 获取所有动态端点
POST   /api/DynamicApiManagement/refresh           # 刷新动态映射
PUT    /api/DynamicApiManagement/endpoints/{id}/toggle  # 启用/禁用端点

# 服务映射
POST   /api/DynamicApiManagement/services/map      # 为服务创建端点

# 统计信息
GET    /api/DynamicApiManagement/statistics        # 获取统计信息
```

## 📝 迁移步骤

### 步骤1：测试新端点
```bash
# 测试获取用户列表
curl -X GET "http://localhost:5143/api/dynamic/users/users?page=1&size=10" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 测试获取端点列表
curl -X GET "http://localhost:5143/api/DynamicApiManagement/endpoints" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### 步骤2：更新客户端代码
```javascript
// 旧代码
const response = await fetch('/api/user?page=1&size=10');

// 新代码
const response = await fetch('/api/dynamic/users/users?page=1&size=10');
```

### 步骤3：验证功能
确保新端点的响应格式和功能与旧端点一致。

### 步骤4：移除旧端点依赖
完成迁移后，可以安全地移除对旧端点的调用。

## 🔧 配置说明

### 动态API配置
```json
{
  "DynamicApiMapping": {
    "BasePrefix": "/api/dynamic",
    "AutoCreateCrudEndpoints": true,
    "RequireAuthenticationByDefault": true,
    "DefaultRequiredRoles": ["User"]
  }
}
```

### 认证要求
- 所有动态API端点默认需要认证
- 默认需要 "User" 角色
- 管理端点需要 "Admin" 角色

## ⚡ 性能优势

### 动态API的优势
1. **自动生成**：无需手动编写Controller代码
2. **运行时管理**：可以动态启用/禁用端点
3. **统一日志**：所有端点使用统一的日志记录
4. **缓存支持**：支持端点级别的缓存配置
5. **限流控制**：支持端点级别的访问限流

### 性能对比
- 减少了Controller层的代码量
- 统一的错误处理和日志记录
- 更好的可观测性和监控

## 🛠️ 故障排除

### 常见问题

#### 1. 新端点返回404
**原因**：动态映射可能未初始化
**解决**：调用刷新端点
```bash
POST /api/DynamicApiManagement/refresh
```

#### 2. 认证失败
**原因**：新端点的认证要求可能不同
**解决**：检查Token和角色权限

#### 3. 参数格式不匹配
**原因**：动态端点的参数映射可能不同
**解决**：查看端点详情确认参数格式

### 调试工具
```bash
# 查看所有可用端点
GET /api/DynamicApiManagement/endpoints

# 查看端点统计
GET /api/DynamicApiManagement/statistics

# 检查应用健康状态
GET /health
```

## 📅 迁移时间表

### 阶段1：并行运行 (当前)
- ✅ 新旧端点同时可用
- ✅ 旧端点标记为废弃
- ✅ 客户端开始迁移测试

### 阶段2：迁移期 (建议2-4周)
- 🔄 客户端逐步迁移到新端点
- 🔄 监控新端点的使用情况
- 🔄 收集反馈和问题

### 阶段3：移除旧端点 (迁移完成后)
- ❌ 注释或删除旧的Controller
- ✅ 只保留动态API端点
- ✅ 更新文档和示例

## 📞 支持

如果在迁移过程中遇到问题：
1. 查看应用日志：检查详细的错误信息
2. 测试端点：使用Postman或curl测试新端点
3. 查看统计：通过管理端点查看使用情况
4. 刷新映射：如果端点不可用，尝试刷新动态映射

## 🎯 最佳实践

1. **渐进式迁移**：不要一次性切换所有端点
2. **充分测试**：在生产环境迁移前充分测试新端点
3. **监控使用**：密切监控新端点的性能和错误率
4. **保留日志**：保留迁移期间的详细日志
5. **回滚准备**：准备快速回滚到旧端点的方案

通过遵循这个迁移指南，您可以安全、平稳地从旧的静态API迁移到新的动态API系统。
