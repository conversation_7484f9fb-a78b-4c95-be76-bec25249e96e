using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using YourWpfApp.Services;

namespace YourWpfApp.ViewModels
{
    /// <summary>
    /// 质检管理ViewModel示例
    /// </summary>
    public class InspectionViewModel : INotifyPropertyChanged
    {
        private readonly WorkReportInspectionApiService _apiService;
        private ObservableCollection<WorkReportInspectionDto> _inspections;
        private WorkReportInspectionDto _selectedInspection;
        private bool _isLoading;
        private string _searchText;

        public InspectionViewModel()
        {
            _apiService = new WorkReportInspectionApiService();
            Inspections = new ObservableCollection<WorkReportInspectionDto>();
            
            // 初始化命令
            LoadDataCommand = new RelayCommand(async () => await LoadDataAsync());
            CreateCommand = new RelayCommand(async () => await CreateInspectionAsync());
            UpdateCommand = new RelayCommand(async () => await UpdateInspectionAsync(), () => SelectedInspection != null);
            DeleteCommand = new RelayCommand(async () => await DeleteInspectionAsync(), () => SelectedInspection != null);
            PerformInspectionCommand = new RelayCommand(async () => await PerformInspectionAsync(), () => SelectedInspection != null);
            SearchCommand = new RelayCommand(async () => await SearchAsync());
        }

        #region 属性

        public ObservableCollection<WorkReportInspectionDto> Inspections
        {
            get => _inspections;
            set
            {
                _inspections = value;
                OnPropertyChanged();
            }
        }

        public WorkReportInspectionDto SelectedInspection
        {
            get => _selectedInspection;
            set
            {
                _selectedInspection = value;
                OnPropertyChanged();
                // 更新命令可用性
                ((RelayCommand)UpdateCommand).RaiseCanExecuteChanged();
                ((RelayCommand)DeleteCommand).RaiseCanExecuteChanged();
                ((RelayCommand)PerformInspectionCommand).RaiseCanExecuteChanged();
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                _searchText = value;
                OnPropertyChanged();
            }
        }

        #endregion

        #region 命令

        public ICommand LoadDataCommand { get; }
        public ICommand CreateCommand { get; }
        public ICommand UpdateCommand { get; }
        public ICommand DeleteCommand { get; }
        public ICommand PerformInspectionCommand { get; }
        public ICommand SearchCommand { get; }

        #endregion

        #region 方法

        /// <summary>
        /// 加载数据
        /// </summary>
        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                
                var searchDto = new SearchDto
                {
                    PageIndex = 1,
                    PageSize = 100
                };

                var result = await _apiService.GetListAsync(searchDto);
                
                if (result.IsSucc)
                {
                    Inspections.Clear();
                    foreach (var item in result.Data.Data)
                    {
                        Inspections.Add(item);
                    }
                }
                else
                {
                    MessageBox.Show($"加载数据失败: {result.Msg}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载数据异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 创建质检记录
        /// </summary>
        private async Task CreateInspectionAsync()
        {
            try
            {
                // 这里应该打开创建窗口，获取用户输入
                var createDto = new CreateWorkReportInspectionDto
                {
                    InspectionCode = "JYDH" + DateTime.Now.ToString("yyyyMMddHHmmss"),
                    InspectionName = "新建检验单",
                    InspectionType = "首检",
                    ProductId = Guid.NewGuid(), // 实际应该从选择器获取
                    ProcessStepId = Guid.NewGuid(),
                    StationId = Guid.NewGuid(),
                    TeamId = Guid.NewGuid(),
                    ReporterId = Guid.NewGuid(),
                    ReportedQuantity = 100,
                    ReportTime = DateTime.Now
                };

                var result = await _apiService.CreateAsync(createDto);
                
                if (result.IsSucc)
                {
                    MessageBox.Show("创建成功", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    await LoadDataAsync(); // 刷新列表
                }
                else
                {
                    MessageBox.Show($"创建失败: {result.Msg}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 更新质检记录
        /// </summary>
        private async Task UpdateInspectionAsync()
        {
            try
            {
                var updateDto = new UpdateWorkReportInspectionDto
                {
                    Id = SelectedInspection.Id,
                    InspectionName = SelectedInspection.InspectionName,
                    Status = SelectedInspection.Status,
                    // 其他需要更新的字段...
                };

                var result = await _apiService.UpdateAsync(updateDto);
                
                if (result.IsSucc)
                {
                    MessageBox.Show("更新成功", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    await LoadDataAsync(); // 刷新列表
                }
                else
                {
                    MessageBox.Show($"更新失败: {result.Msg}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 删除质检记录
        /// </summary>
        private async Task DeleteInspectionAsync()
        {
            try
            {
                var result = MessageBox.Show("确定要删除这条记录吗？", "确认", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    var success = await _apiService.DeleteAsync(SelectedInspection.Id);
                    
                    if (success)
                    {
                        MessageBox.Show("删除成功", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                        await LoadDataAsync(); // 刷新列表
                    }
                    else
                    {
                        MessageBox.Show("删除失败", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 执行质检
        /// </summary>
        private async Task PerformInspectionAsync()
        {
            try
            {
                var updateDto = new UpdateWorkReportInspectionDto
                {
                    Id = SelectedInspection.Id,
                    InspectorId = Guid.NewGuid(), // 实际应该是当前用户ID
                    InspectionTime = DateTime.Now,
                    Status = "已质检",
                    TestedQuantity = 50,
                    QualifiedQuantity = 45,
                    UnqualifiedQuantity = 5,
                    OverallResult = "合格",
                    InspectionDepartment = "质检部门"
                };

                var result = await _apiService.PerformInspectionAsync(SelectedInspection.Id, updateDto);
                
                if (result.IsSucc)
                {
                    MessageBox.Show("质检完成", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    await LoadDataAsync(); // 刷新列表
                }
                else
                {
                    MessageBox.Show($"质检失败: {result.Msg}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"质检异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 搜索
        /// </summary>
        private async Task SearchAsync()
        {
            try
            {
                IsLoading = true;
                
                var searchDto = new SearchDto
                {
                    PageIndex = 1,
                    PageSize = 100,
                    InspectionName = SearchText
                };

                var result = await _apiService.GetListAsync(searchDto);
                
                if (result.IsSucc)
                {
                    Inspections.Clear();
                    foreach (var item in result.Data.Data)
                    {
                        Inspections.Add(item);
                    }
                }
                else
                {
                    MessageBox.Show($"搜索失败: {result.Msg}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"搜索异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    /// <summary>
    /// 简单的RelayCommand实现
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Func<Task> _executeAsync;
        private readonly Func<bool> _canExecute;

        public RelayCommand(Func<Task> executeAsync, Func<bool> canExecute = null)
        {
            _executeAsync = executeAsync;
            _canExecute = canExecute;
        }

        public event EventHandler CanExecuteChanged;

        public bool CanExecute(object parameter)
        {
            return _canExecute?.Invoke() ?? true;
        }

        public async void Execute(object parameter)
        {
            await _executeAsync();
        }

        public void RaiseCanExecuteChanged()
        {
            CanExecuteChanged?.Invoke(this, EventArgs.Empty);
        }
    }
}
