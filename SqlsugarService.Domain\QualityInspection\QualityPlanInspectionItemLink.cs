using SqlSugar;
using SqlsugarService.Domain.Common;
using System;

namespace SqlsugarService.Domain.QualityInspection
{
    /// <summary>
    /// 质检方案与检验项目的中间表
    /// </summary>
    public class QualityPlanInspectionItemLink : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 质检方案Id
        /// </summary>
        public Guid QualityPlanId { get; set; }

        /// <summary>
        /// 检验项目Id
        /// </summary>
        public Guid InspectionItemId { get; set; }
    }
} 