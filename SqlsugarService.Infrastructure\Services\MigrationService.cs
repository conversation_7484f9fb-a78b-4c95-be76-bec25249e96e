using SqlSugar;
using SqlsugarService.Infrastructure.Migrations;

namespace SqlsugarService.Infrastructure.Services
{
    /// <summary>
    /// 数据库迁移服务
    /// </summary>
    public class MigrationService
    {
        private readonly ISqlSugarClient _db;

        public MigrationService(ISqlSugarClient db)
        {
            _db = db;
        }

        /// <summary>
        /// 执行所有待执行的迁移
        /// </summary>
        public async Task RunMigrationsAsync()
        {
            try
            {
                Console.WriteLine("开始执行数据库迁移...");

                // 执行质检表字段添加迁移
                var workReportInspectionMigration = new AddWorkReportInspectionFields(_db);
                await workReportInspectionMigration.UpAsync();
                
                // 验证迁移结果
                var isValid = await workReportInspectionMigration.ValidateAsync();
                if (!isValid)
                {
                    throw new Exception("质检表字段迁移验证失败");
                }

                Console.WriteLine("所有数据库迁移执行完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"数据库迁移执行失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 回滚迁移
        /// </summary>
        public async Task RollbackMigrationsAsync()
        {
            try
            {
                Console.WriteLine("开始回滚数据库迁移...");

                // 回滚质检表字段迁移
                var workReportInspectionMigration = new AddWorkReportInspectionFields(_db);
                await workReportInspectionMigration.DownAsync();

                Console.WriteLine("数据库迁移回滚完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"数据库迁移回滚失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 检查数据库连接
        /// </summary>
        public async Task<bool> CheckDatabaseConnectionAsync()
        {
            try
            {
                await _db.Ado.ExecuteCommandAsync("SELECT 1");
                Console.WriteLine("数据库连接正常");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"数据库连接失败: {ex.Message}");
                return false;
            }
        }
    }
}