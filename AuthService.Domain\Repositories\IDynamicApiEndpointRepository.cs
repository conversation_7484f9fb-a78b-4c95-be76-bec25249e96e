using AuthService.Domain.Entities;
using AuthService.Domain.Enums;
using HttpMethod = AuthService.Domain.Enums.HttpMethod;

namespace AuthService.Domain.Repositories;

/// <summary>
/// 动态API端点仓储接口
/// 定义动态API端点数据访问的抽象方法
/// </summary>
public interface IDynamicApiEndpointRepository
{
    /// <summary>
    /// 根据ID获取API端点
    /// </summary>
    /// <param name="id">端点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>API端点实体或null</returns>
    Task<DynamicApiEndpoint?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据路径和方法获取API端点
    /// </summary>
    /// <param name="pathTemplate">路径模板</param>
    /// <param name="method">HTTP方法</param>
    /// <param name="tenantId">租户ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>API端点实体或null</returns>
    Task<DynamicApiEndpoint?> GetByPathAndMethodAsync(string pathTemplate, HttpMethod method, string? tenantId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据路径和方法获取API端点（字符串方法参数重载）
    /// </summary>
    /// <param name="pathTemplate">路径模板</param>
    /// <param name="method">HTTP方法字符串</param>
    /// <param name="tenantId">租户ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>API端点列表</returns>
    Task<IEnumerable<DynamicApiEndpoint>> GetByPathAndMethodAsync(string pathTemplate, string method, string? tenantId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取匹配路径的API端点列表
    /// </summary>
    /// <param name="requestPath">请求路径</param>
    /// <param name="method">HTTP方法</param>
    /// <param name="tenantId">租户ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>匹配的API端点列表（按优先级排序）</returns>
    Task<IEnumerable<DynamicApiEndpoint>> GetMatchingEndpointsAsync(string requestPath, HttpMethod method, string? tenantId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有启用的API端点
    /// </summary>
    /// <param name="tenantId">租户ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启用的API端点列表</returns>
    Task<IEnumerable<DynamicApiEndpoint>> GetEnabledEndpointsAsync(string? tenantId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取API端点列表（分页）
    /// </summary>
    /// <param name="pageNumber">页码（从1开始）</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="searchTerm">搜索关键词（可选）</param>
    /// <param name="tenantId">租户ID（可选）</param>
    /// <param name="isEnabled">是否启用（可选）</param>
    /// <param name="method">HTTP方法（可选）</param>
    /// <param name="tags">标签列表（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>API端点列表和总数</returns>
    Task<(IEnumerable<DynamicApiEndpoint> Endpoints, int TotalCount)> GetPagedAsync(
        int pageNumber = 1,
        int pageSize = 20,
        string? searchTerm = null,
        string? tenantId = null,
        bool? isEnabled = null,
        HttpMethod? method = null,
        IEnumerable<string>? tags = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据目标服务获取API端点列表
    /// </summary>
    /// <param name="targetService">目标服务名称</param>
    /// <param name="tenantId">租户ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>API端点列表</returns>
    Task<IEnumerable<DynamicApiEndpoint>> GetByTargetServiceAsync(string targetService, string? tenantId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据标签获取API端点列表
    /// </summary>
    /// <param name="tag">标签名称</param>
    /// <param name="tenantId">租户ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>API端点列表</returns>
    Task<IEnumerable<DynamicApiEndpoint>> GetByTagAsync(string tag, string? tenantId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查路径和方法组合是否已存在
    /// </summary>
    /// <param name="pathTemplate">路径模板</param>
    /// <param name="method">HTTP方法</param>
    /// <param name="tenantId">租户ID（可选）</param>
    /// <param name="excludeId">排除的端点ID（用于更新时检查）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsByPathAndMethodAsync(string pathTemplate, HttpMethod method, string? tenantId = null, Guid? excludeId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 创建API端点
    /// </summary>
    /// <param name="endpoint">API端点实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的API端点实体</returns>
    Task<DynamicApiEndpoint> CreateAsync(DynamicApiEndpoint endpoint, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新API端点
    /// </summary>
    /// <param name="endpoint">API端点实体</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新的API端点实体</returns>
    Task<DynamicApiEndpoint> UpdateAsync(DynamicApiEndpoint endpoint, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除API端点（软删除）
    /// </summary>
    /// <param name="id">端点ID</param>
    /// <param name="deletedBy">删除者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功</returns>
    Task<bool> DeleteAsync(Guid id, string? deletedBy = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 物理删除API端点
    /// </summary>
    /// <param name="id">端点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功</returns>
    Task<bool> HardDeleteAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量创建API端点
    /// </summary>
    /// <param name="endpoints">API端点列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的API端点列表</returns>
    Task<IEnumerable<DynamicApiEndpoint>> CreateBatchAsync(IEnumerable<DynamicApiEndpoint> endpoints, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量更新API端点
    /// </summary>
    /// <param name="endpoints">API端点列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新的API端点列表</returns>
    Task<IEnumerable<DynamicApiEndpoint>> UpdateBatchAsync(IEnumerable<DynamicApiEndpoint> endpoints, CancellationToken cancellationToken = default);

    /// <summary>
    /// 记录API端点访问
    /// </summary>
    /// <param name="id">端点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功</returns>
    Task<bool> RecordAccessAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取API端点统计信息
    /// </summary>
    /// <param name="tenantId">租户ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    Task<ApiEndpointStatistics> GetStatisticsAsync(string? tenantId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取最热门的API端点
    /// </summary>
    /// <param name="count">数量</param>
    /// <param name="tenantId">租户ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>API端点列表</returns>
    Task<IEnumerable<DynamicApiEndpoint>> GetMostPopularAsync(int count = 10, string? tenantId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取最近访问的API端点
    /// </summary>
    /// <param name="count">数量</param>
    /// <param name="tenantId">租户ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>API端点列表</returns>
    Task<IEnumerable<DynamicApiEndpoint>> GetRecentlyAccessedAsync(int count = 10, string? tenantId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有可用的标签
    /// </summary>
    /// <param name="tenantId">租户ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>标签列表</returns>
    Task<IEnumerable<string>> GetAllTagsAsync(string? tenantId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有目标服务
    /// </summary>
    /// <param name="tenantId">租户ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>目标服务列表</returns>
    Task<IEnumerable<string>> GetAllTargetServicesAsync(string? tenantId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有API端点
    /// </summary>
    /// <param name="tenantId">租户ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>API端点列表</returns>
    Task<IEnumerable<DynamicApiEndpoint>> GetAllAsync(string? tenantId = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// API端点统计信息
/// </summary>
public class ApiEndpointStatistics
{
    /// <summary>
    /// 总端点数
    /// </summary>
    public int TotalEndpoints { get; set; }

    /// <summary>
    /// 启用端点数
    /// </summary>
    public int EnabledEndpoints { get; set; }

    /// <summary>
    /// 禁用端点数
    /// </summary>
    public int DisabledEndpoints { get; set; }

    /// <summary>
    /// 今日新增端点数
    /// </summary>
    public int TodayNewEndpoints { get; set; }

    /// <summary>
    /// 本周新增端点数
    /// </summary>
    public int WeekNewEndpoints { get; set; }

    /// <summary>
    /// 本月新增端点数
    /// </summary>
    public int MonthNewEndpoints { get; set; }

    /// <summary>
    /// 总访问次数
    /// </summary>
    public long TotalAccesses { get; set; }

    /// <summary>
    /// 今日访问次数
    /// </summary>
    public long TodayAccesses { get; set; }

    /// <summary>
    /// 按HTTP方法分组的统计
    /// </summary>
    public Dictionary<HttpMethod, int> EndpointsByMethod { get; set; } = new();

    /// <summary>
    /// 按目标服务分组的统计
    /// </summary>
    public Dictionary<string, int> EndpointsByService { get; set; } = new();
}
