using SqlsugarService.Domain.QualityInspection;

namespace SqlsugarService.Infrastructure.IRepository
{
    /// <summary>
    /// 质检仓储接口
    /// </summary>
    public interface IWorkReportInspectionRepository : IBaseRepository<WorkReportInspectionEntity>
    {
        /// <summary>
        /// 插入并返回实体
        /// </summary>
        /// <param name="entity">实体对象</param>
        /// <returns>插入后的实体</returns>
        Task<WorkReportInspectionEntity> InsertReturnEntityAsync(WorkReportInspectionEntity entity);

        /// <summary>
        /// 根据ID删除
        /// </summary>
        /// <param name="id">ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteByIdAsync(Guid id);

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="ids">ID数组</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteByIdsAsync(Guid[] ids);
    }
}