using SqlsugarService.Application.Until;
using System;

namespace SqlsugarService.Application.DTOs.WorkReportInspectionDto
{
    /// <summary>
    /// 报工质检高级搜索参数DTO
    /// 支持通过关联实体的字段进行搜索
    /// </summary>
    public class WorkReportInspectionAdvancedSearchDto : Seach
    {
        #region 基础搜索条件
        /// <summary>
        /// 检验单号
        /// </summary>
        public string? InspectionCode { get; set; }

        /// <summary>
        /// 检验单名称
        /// </summary>
        public string? InspectionName { get; set; }

        /// <summary>
        /// 检验类型 (如：首检, 巡检, 末检)
        /// </summary>
        public string? InspectionType { get; set; }

        /// <summary>
        /// 状态 (如: 未质检, 已质检, 已完检)
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 检验部门
        /// </summary>
        public string? InspectionDepartment { get; set; }

        /// <summary>
        /// 检测结果 (如: 合格, 不合格)
        /// </summary>
        public string? OverallResult { get; set; }
        #endregion

        #region 关联实体搜索条件
        /// <summary>
        /// 产品名称 (通过Product表搜索)
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 产品编号 (通过Product表搜索)
        /// </summary>
        public string? ProductCode { get; set; }

        /// <summary>
        /// 工序名称 (通过ProcessStep表搜索)
        /// </summary>
        public string? ProcessStepName { get; set; }

        /// <summary>
        /// 站点名称 (通过Station表搜索)
        /// </summary>
        public string? StationName { get; set; }

        /// <summary>
        /// 班组名称 (通过Team表搜索)
        /// </summary>
        public string? TeamName { get; set; }

        /// <summary>
        /// 报工人员姓名 (通过Reporter表搜索)
        /// </summary>
        public string? ReporterName { get; set; }

        /// <summary>
        /// 检验人员姓名 (通过Inspector表搜索)
        /// </summary>
        public string? InspectorName { get; set; }

        /// <summary>
        /// 工单名称 (通过WorkOrder表搜索)
        /// </summary>
        public string? WorkOrderName { get; set; }

        /// <summary>
        /// 工单编号 (通过WorkOrder表搜索)
        /// </summary>
        public string? WorkOrderCode { get; set; }

        /// <summary>
        /// 任务名称 (通过Task表搜索)
        /// </summary>
        public string? TaskName { get; set; }

        /// <summary>
        /// 任务编号 (通过Task表搜索)
        /// </summary>
        public string? TaskCode { get; set; }
        #endregion

        #region 时间范围搜索
        /// <summary>
        /// 报工开始时间
        /// </summary>
        public DateTime? ReportTimeStart { get; set; }

        /// <summary>
        /// 报工结束时间
        /// </summary>
        public DateTime? ReportTimeEnd { get; set; }

        /// <summary>
        /// 检验开始时间
        /// </summary>
        public DateTime? InspectionTimeStart { get; set; }

        /// <summary>
        /// 检验结束时间
        /// </summary>
        public DateTime? InspectionTimeEnd { get; set; }
        #endregion

        #region 外键ID搜索
        /// <summary>
        /// 产品Id
        /// </summary>
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 工序Id
        /// </summary>
        public Guid? ProcessStepId { get; set; }

        /// <summary>
        /// 站点Id
        /// </summary>
        public Guid? StationId { get; set; }

        /// <summary>
        /// 班组Id
        /// </summary>
        public Guid? TeamId { get; set; }

        /// <summary>
        /// 报工人员Id
        /// </summary>
        public Guid? ReporterId { get; set; }

        /// <summary>
        /// 检验人员Id
        /// </summary>
        public Guid? InspectorId { get; set; }

        /// <summary>
        /// 工单Id
        /// </summary>
        public Guid? WorkOrderId { get; set; }

        /// <summary>
        /// 任务Id
        /// </summary>
        public Guid? TaskId { get; set; }
        #endregion

        #region 数量范围搜索
        /// <summary>
        /// 最小报工数量
        /// </summary>
        public int? MinReportedQuantity { get; set; }

        /// <summary>
        /// 最大报工数量
        /// </summary>
        public int? MaxReportedQuantity { get; set; }

        /// <summary>
        /// 最小检测数量
        /// </summary>
        public int? MinTestedQuantity { get; set; }

        /// <summary>
        /// 最大检测数量
        /// </summary>
        public int? MaxTestedQuantity { get; set; }

        /// <summary>
        /// 最小合格率 (百分比)
        /// </summary>
        public decimal? MinQualificationRate { get; set; }

        /// <summary>
        /// 最大合格率 (百分比)
        /// </summary>
        public decimal? MaxQualificationRate { get; set; }
        #endregion
    }
}
