﻿using SqlsugarService.Application.DTOs.SalesDto;
using SqlsugarService.Application.Until;
using SqlsugarService.Domain.InventoryChange;

namespace SqlsugarService.Application.IService.Sales
{
    public interface ISalesService
    {
        Task<ApiResult<PageResult<List<getsalesorderDto>>>> GetSalesOrderList(GetsalesorderSearchDto seach);
        Task<ApiResult> AddSalesOrder(insertupdatesalesorderDto salesoutbounddto);
    }
}