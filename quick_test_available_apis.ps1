# 快速测试当前可用的质检API接口
# 验证修复后哪些功能可以正常使用

$baseUrl = "https://localhost:7001/api/WorkReportInspection"

Write-Host "=== 质检API可用功能测试 ===" -ForegroundColor Green
Write-Host "注意：部分功能因数据库字段缺失而受限" -ForegroundColor Yellow

# 测试基础选项接口
Write-Host "`n📋 测试基础选项接口..." -ForegroundColor Cyan

$tests = @(
    @{ Name = "状态选项"; Url = "$baseUrl/status-options" },
    @{ Name = "检验类型"; Url = "$baseUrl/inspection-types" },
    @{ Name = "检测结果"; Url = "$baseUrl/result-options" }
)

foreach ($test in $tests) {
    try {
        $response = Invoke-RestMethod -Uri $test.Url -Method GET -ContentType "application/json" -SkipCertificateCheck
        Write-Host "  ✅ $($test.Name): 成功 ($($response.data.Count) 项)" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ $($test.Name): 失败 - $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试数据查询接口
Write-Host "`n📊 测试数据查询接口..." -ForegroundColor Cyan

$dataTests = @(
    @{ Name = "简单列表"; Url = "$baseUrl/simple-list"; Method = "GET" },
    @{ Name = "关联数据检查"; Url = "$baseUrl/check-related-data"; Method = "GET" },
    @{ Name = "简单分页查询"; Url = "$baseUrl/simple-list-paged?pageIndex=1&pageSize=5"; Method = "GET" }
)

foreach ($test in $dataTests) {
    try {
        $response = Invoke-RestMethod -Uri $test.Url -Method $test.Method -ContentType "application/json" -SkipCertificateCheck
        if ($test.Name -eq "简单列表") {
            Write-Host "  ✅ $($test.Name): 成功 ($($response.data.Count) 条记录)" -ForegroundColor Green
        } elseif ($test.Name -eq "关联数据检查") {
            Write-Host "  ✅ $($test.Name): 成功 (总记录: $($response.data.TotalInspectionRecords))" -ForegroundColor Green
        } else {
            Write-Host "  ✅ $($test.Name): 成功 (总数: $($response.data.totalCount), 当前页: $($response.data.data.Count))" -ForegroundColor Green
        }
    } catch {
        Write-Host "  ❌ $($test.Name): 失败 - $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试高级搜索接口（基础功能）
Write-Host "`n🔍 测试高级搜索接口（基础功能）..." -ForegroundColor Cyan

$searchBody = @{
    pageIndex = 1
    pageSize = 5
    inspectionCode = ""
    inspectionName = ""
    status = ""
    inspectionType = ""
    overallResult = ""
    # 注意：不测试计划、工单、任务相关字段，因为它们暂时被忽略
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/list" -Method POST -Body $searchBody -ContentType "application/json" -SkipCertificateCheck
    Write-Host "  ✅ 高级搜索: 成功 (总数: $($response.data.totalCount), 当前页: $($response.data.data.Count))" -ForegroundColor Green
} catch {
    Write-Host "  ❌ 高级搜索: 失败 - $($_.Exception.Message)" -ForegroundColor Red
}

# 显示受限功能说明
Write-Host "`n⚠️  当前受限的功能:" -ForegroundColor Yellow
Write-Host "   • 计划编号搜索 (planNumber)" -ForegroundColor Gray
Write-Host "   • 工单相关搜索 (workOrderName, workOrderCode)" -ForegroundColor Gray
Write-Host "   • 任务相关搜索 (taskName, taskCode)" -ForegroundColor Gray
Write-Host "   • 相关字段的显示和关联查询" -ForegroundColor Gray

Write-Host "`n🔧 恢复完整功能的步骤:" -ForegroundColor Cyan
Write-Host "   1. 执行数据库迁移脚本: fix_database_fields.sql" -ForegroundColor White
Write-Host "   2. 从实体类中移除 [SugarColumn(IsIgnore = true)] 注解" -ForegroundColor White
Write-Host "   3. 重新编译和测试项目" -ForegroundColor White

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "基础功能正常，可以开始使用！" -ForegroundColor Cyan