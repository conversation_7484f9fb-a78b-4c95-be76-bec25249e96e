﻿using AutoMapper;
using SqlsugarService.Application.DTOs.PlanDto;
using SqlsugarService.Application.IService.Plan;
using SqlsugarService.Application.Until;
using SqlsugarService.Domain.InventoryChange;
using SqlsugarService.Domain.Materials;
using SqlsugarService.Domain.Plan;
using SqlsugarService.Infrastructure.IRepository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.Service.Plan
{
    public class ProductionPlanService : IProductionPlanService
    {
        private readonly IBaseRepository<ProductionPlan> baseplan;
        private readonly IBaseRepository<ProductEntity> entitybase;
        private readonly IBaseRepository<Salesorder> orderbase;
        private readonly IMapper mapper;

        public ProductionPlanService(IBaseRepository<ProductionPlan> baseplan, IBaseRepository<ProductEntity> entitybase, IBaseRepository<Salesorder> orderbase, IMapper mapper)
        {
            this.baseplan = baseplan;
            this.entitybase = entitybase;
            this.orderbase = orderbase;
            this.mapper = mapper;
        }


        /// <summary>
        /// 新增生产计划
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public async Task<ApiResult> AddProductionPlan(InsertupdateproductionplanDto dto)
        {
            // 参数校验
            if (dto == null)
                return ApiResult.Fail("参数不能为空", ResultCode.Error);

            try
            {
                // 根据当前时间生成唯一编号
                dto.ProductNumber = GenerateProductNumberByTime();
                dto.Id= Guid.NewGuid();
                var res = mapper.Map<InsertupdateproductionplanDto, ProductionPlan>(dto);
                var result = await baseplan.InsertAsync(res);
                return result ? ApiResult.Success(ResultCode.Success) : ApiResult.Fail("新增失败", ResultCode.Error);
            }
            catch (Exception ex)
            {
                // 记录日志（此处省略日志记录逻辑）
                return ApiResult.Fail("系统异常：" + ex.Message, ResultCode.Error);
            }
        }

        /// <summary>
        /// 根据当前时间生成唯一的 ProductNumber，格式为 D + yyyyMMddHHmmss
        /// </summary>
        /// <returns></returns>
        private string GenerateProductNumberByTime()
        {
            var now = DateTime.Now;
            var timePart = now.ToString("yyyyMMddHHmmssfff");
            return $"SCJH{timePart}";
        }
        /// <summary>
        /// 修改生产计划
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public async Task<ApiResult> UpdateProductionPlan(InsertupdateproductionplanDto dto)
        {
            var res = mapper.Map<InsertupdateproductionplanDto, ProductionPlan>(dto);
            var result = await baseplan.UpdateAsync(res);
            return result ? ApiResult.Success(ResultCode.Success) : ApiResult.Fail("更新失败", ResultCode.Error);
        }
        /// <summary>
        /// 显示生产计划列表(分页)
        /// </summary>
        /// <param name="seach">分页查询参数</param>
        /// <returns>分页结果</returns>
        public async Task<ApiResult<PageResult<List<GetproductionplanDto>>>> GetProductionPlanList(GetproductionplanSearchDto seach)
        {
            try
            {
                // 获取分页数据
                var pagedList = await baseplan.GetAllAsync();
                if(!string.IsNullOrEmpty(seach.PlanNumber))
                {
                    pagedList = pagedList.Where(x => x.ProductNumber.Contains(seach.PlanNumber) || x.ProductName.Contains(seach.PlanNumber)).ToList();
                }
                if (!string.IsNullOrEmpty(seach.Unit))
                {
                    pagedList = pagedList.Where(x => x.Unit.Contains(seach.Unit)).ToList();
                }
                if (seach.Status!=null)
                {
                    pagedList = pagedList.Where(x => x.Status==seach.Status).ToList();
                }
                var totalCount = pagedList.Count();
                var totalPage = (int)Math.Ceiling((double)totalCount / seach.PageSize);
                var res = pagedList.Skip((seach.PageIndex - 1) * seach.PageSize).Take(seach.PageSize).ToList();
                // 映射为DTO
                var mappedList = mapper.Map<List<GetproductionplanDto>>(res);
                // 构建分页结果
                var result = new PageResult<List<GetproductionplanDto>>
                {
                    Data = mappedList,
                    TotalCount = totalCount,
                    TotalPage = totalPage,
                };

                return ApiResult<PageResult<List<GetproductionplanDto>>>.Success(result, ResultCode.Success);
            }
            catch (Exception ex)
            {
                // 记录日志
                return ApiResult<PageResult<List<GetproductionplanDto>>>.Fail($"查询失败：{ex.Message}", ResultCode.Error);
            }
        }
        /// <summary>
        /// 删除生产计划
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResult> DeleteProductionPlan(Guid id)
        {
            var result = await baseplan.SoftDeleteAsync(id);
            return result ? ApiResult.Success(ResultCode.Success) : ApiResult.Fail("删除失败", ResultCode.Error);
        }
    }
}
