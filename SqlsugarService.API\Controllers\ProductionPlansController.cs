﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SqlsugarService.Application.DTOs.PlanDto;
using SqlsugarService.Application.IService.Plan;
using SqlsugarService.Application.Until;

namespace SqlsugarService.API.Controllers
{
    /// <summary>
    /// 生产计划
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ProductionPlansController : ControllerBase
    {
        private readonly IProductionPlanService _productionPlanService;

        public ProductionPlansController(IProductionPlanService productionPlanService)
        {
            _productionPlanService = productionPlanService;
        }
        /// <summary>
        /// 新增生产计划
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ApiResult> AddProductionPlan(InsertupdateproductionplanDto dto)
        {
            return await _productionPlanService.AddProductionPlan(dto);
        }

        /// <summary>
        /// 显示生产计划列表(分页)
        /// </summary>
        /// <param name="seach">分页查询参数</param>
        /// <returns>分页结果</returns>
        [HttpGet]
        public async Task<ApiResult<PageResult<List<GetproductionplanDto>>>> GetProductionPlanList([FromQuery] GetproductionplanSearchDto seach)
        {
            return await _productionPlanService.GetProductionPlanList(seach);
        }
    }
}
