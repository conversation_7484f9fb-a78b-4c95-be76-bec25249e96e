# 前端质检API最终测试
$baseUrl = "http://localhost:64922"

Write-Host "=== 前端质检API测试 ===" -ForegroundColor Cyan

# 1. 测试状态选项API
Write-Host "`n1. 测试状态选项API..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/WorkReportInspection/status-options" -Method GET
    Write-Host "状态选项API成功" -ForegroundColor Green
    Write-Host "返回状态选项数量: $($response.data.Count)" -ForegroundColor Gray
} catch {
    Write-Host "状态选项API失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 测试基础列表查询
Write-Host "`n2. 测试基础列表查询..." -ForegroundColor Yellow
try {
    $body = '{"pageIndex": 1, "pageSize": 3}'
    $response = Invoke-RestMethod -Uri "$baseUrl/api/WorkReportInspection/list" -Method POST -Body $body -ContentType "application/json"
    Write-Host "基础列表查询成功" -ForegroundColor Green
    Write-Host "总记录数: $($response.data.totalCount)" -ForegroundColor Gray
    Write-Host "当前页记录数: $($response.data.data.Count)" -ForegroundColor Gray
} catch {
    Write-Host "基础列表查询失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 测试状态过滤查询
Write-Host "`n3. 测试状态过滤查询..." -ForegroundColor Yellow
try {
    $body = '{"pageIndex": 1, "pageSize": 3, "status": "合格"}'
    $response = Invoke-RestMethod -Uri "$baseUrl/api/WorkReportInspection/list" -Method POST -Body $body -ContentType "application/json"
    Write-Host "状态过滤查询成功" -ForegroundColor Green
    Write-Host "过滤后记录数: $($response.data.totalCount)" -ForegroundColor Gray
} catch {
    Write-Host "状态过滤查询失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 测试产品名称查询
Write-Host "`n4. 测试产品名称查询..." -ForegroundColor Yellow
try {
    $body = '{"pageIndex": 1, "pageSize": 3, "productName": "产品"}'
    $response = Invoke-RestMethod -Uri "$baseUrl/api/WorkReportInspection/list" -Method POST -Body $body -ContentType "application/json"
    Write-Host "产品名称查询成功" -ForegroundColor Green
    Write-Host "匹配记录数: $($response.data.totalCount)" -ForegroundColor Gray
} catch {
    Write-Host "产品名称查询失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Cyan
Write-Host "前端质检API基本功能验证完成" -ForegroundColor Green
