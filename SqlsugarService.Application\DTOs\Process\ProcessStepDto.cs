using SqlsugarService.Domain.Craftsmanship;
using System;
using System.ComponentModel.DataAnnotations;

namespace SqlsugarService.Application.DTOs.Process
{
    /// <summary>
    /// 工序数据传输对象
    /// </summary>
    public class ProcessStepDto
    {
        /// <summary>
        /// 工序编号（必填）
        /// </summary>
        [Required(ErrorMessage = "工序编号不能为空")]
        [StringLength(50, ErrorMessage = "工序编号长度不能超过50个字符")]
        public string ProcessStepNumber { get; set; } = string.Empty;

        /// <summary>
        /// 工序名称（必填）
        /// </summary>
        [Required(ErrorMessage = "工序名称不能为空")]
        [StringLength(200, ErrorMessage = "工序名称长度不能超过200个字符")]
        public string ProcessStepName { get; set; } = string.Empty;

        /// <summary>
        /// 状态（启用/禁用，默认启用）
        /// </summary>
        public StepStatus Status { get; set; } = StepStatus.Enabled;

        /// <summary>
        /// 工序说明（可选）
        /// </summary>
        [StringLength(1000, ErrorMessage = "工序说明长度不能超过1000个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 备注（可选）
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remark { get; set; }

        /// <summary>
        /// 是否系统编号（默认false）
        /// </summary>
        public bool IsSystemNumber { get; set; } = false;

        /// <summary>
        /// 是否关键工序（默认false）
        /// </summary>
        public bool IsKeyStep { get; set; } = false;

        /// <summary>
        /// 准备时间（分钟，默认0）
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "准备时间不能为负数")]
        public int PreparationTime { get; set; } = 0;

        /// <summary>
        /// 等待时间（分钟，默认0）
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "等待时间不能为负数")]
        public int WaitingTime { get; set; } = 0;

        /// <summary>
        /// 颜色（可选，如#1890FF）
        /// </summary>
        [StringLength(20, ErrorMessage = "颜色长度不能超过20个字符")]
        public string? Color { get; set; }

        /// <summary>
        /// 子BOM的工序ID（可选）
        /// </summary>
        public Guid? SubBomId { get; set; }
    }

    /// <summary>
    /// 工序列表显示DTO
    /// </summary>
    public class ProcessStepListDto
    {
        /// <summary>
        /// 工序ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 工序编号
        /// </summary>
        public string ProcessStepNumber { get; set; } = string.Empty;

        /// <summary>
        /// 工序名称
        /// </summary>
        public string ProcessStepName { get; set; } = string.Empty;

        /// <summary>
        /// 状态
        /// </summary>
        public StepStatus Status { get; set; }

        /// <summary>
        /// 状态名称
        /// </summary>
        public string StatusName { get; set; } = string.Empty;

        /// <summary>
        /// 工序说明
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 是否系统编号
        /// </summary>
        public bool IsSystemNumber { get; set; }

        /// <summary>
        /// 是否关键工序
        /// </summary>
        public bool IsKeyStep { get; set; }

        /// <summary>
        /// 准备时间（分钟）
        /// </summary>
        public int PreparationTime { get; set; }

        /// <summary>
        /// 等待时间（分钟）
        /// </summary>
        public int WaitingTime { get; set; }

        /// <summary>
        /// 颜色
        /// </summary>
        public string? Color { get; set; }

        /// <summary>
        /// 子BOM的工序ID
        /// </summary>
        public Guid? SubBomId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 被使用的次数（在多少个工艺路线中使用）
        /// </summary>
        public int UsageCount { get; set; }
    }
}