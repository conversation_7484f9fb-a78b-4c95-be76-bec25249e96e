﻿using SqlSugar;
using SqlsugarService.Domain.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Domain.Craftsmanship
{
    /// <summary>
    /// 工序组成(工艺路线-工序关联列表)
    /// </summary>

    public class ProcessRouteStep : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 工艺路线Id
        /// </summary>
        public Guid ProcessRouteId { get; set; }
        //public ProcessRouteEntity? ProcessRoute { get; set; }

        /// <summary>
        /// 工序Id
        /// </summary>
        public Guid ProcessStepId { get; set; }
        //public ProcessStep? ProcessStep { get; set; }

        public Guid NextProcessStepId { get; set; }

        /// <summary>
        /// 工序顺序号
        /// </summary>
        public int StepOrder { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// 是否当前有效版本
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }
}
