# 📊 AuthService 系统架构图表

本目录包含了 AuthService 项目的系统架构图表源码和使用说明。

## 📁 文件列表

- `system-architecture-diagram.mmd` - 系统架构和请求流程图
- `technology-stack-diagram.mmd` - 技术栈和数据流程图
- `README.md` - 本说明文档

## 🖼️ 图表下载方法

### 方法1：使用 Mermaid Live Editor（推荐）

1. 访问 [Mermaid Live Editor](https://mermaid.live)
2. 复制 `.mmd` 文件中的代码到编辑器
3. 点击 "Actions" → "Download PNG" 或 "Download SVG"

### 方法2：使用 Mermaid CLI

如果您安装了 Mermaid CLI：

```bash
# 安装 Mermaid CLI
npm install -g @mermaid-js/mermaid-cli

# 生成 PNG 图片
mmdc -i system-architecture-diagram.mmd -o system-architecture.png

# 生成 SVG 图片
mmdc -i technology-stack-diagram.mmd -o technology-stack.svg
```

### 方法3：在 VS Code 中预览和导出

1. 安装 VS Code 扩展：`Mermaid Preview`
2. 打开 `.mmd` 文件
3. 使用 `Ctrl+Shift+P` → "Mermaid: Preview"
4. 在预览窗口中右键 → "Save as Image"

### 方法4：使用在线工具

- [Mermaid Live Editor](https://mermaid.live) - 官方在线编辑器
- [Mermaid Chart](https://www.mermaidchart.com/) - 高级图表工具
- [Draw.io](https://app.diagrams.net/) - 支持 Mermaid 导入

## 🎨 图表说明

### 系统架构图 (system-architecture-diagram.mmd)

**用途**: 展示 AuthService 的完整系统架构和请求处理流程

**包含内容**:
- 洋葱架构的四个层次
- 外部系统依赖 (Kong, Consul, PostgreSQL)
- 请求处理流程 (1-10步)
- 服务发现和健康检查流程
- 数据库迁移流程

**颜色编码**:
- 🔵 蓝色：外部系统
- 🟣 紫色：表示层 (API)
- 🟢 绿色：应用层
- 🟠 橙色：基础设施层
- 🔴 红色：领域层

### 技术栈图 (technology-stack-diagram.mmd)

**用途**: 展示技术栈组件关系和数据流向

**包含内容**:
- 网络层技术 (HTTP, CORS, JWT)
- API网关层 (Kong)
- 服务发现层 (Consul)
- 应用服务层 (.NET 9.0, ASP.NET Core)
- 数据访问层 (Dapper, FluentMigrator)
- 数据存储层 (PostgreSQL)

**数据流向**:
- 请求处理流程
- 服务注册发现
- 数据库访问链路

## 🔧 自定义图表

### 修改图表样式

您可以修改 `.mmd` 文件中的样式定义：

```mermaid
%% 样式定义示例
classDef myStyle fill:#f9f,stroke:#333,stroke-width:4px
class NodeName myStyle
```

### 添加新节点

```mermaid
%% 添加新的组件
NewComponent[🆕 新组件<br/>• 功能1<br/>• 功能2]

%% 添加连接
ExistingNode --> NewComponent
```

### 修改颜色主题

```mermaid
%% 自定义颜色主题
classDef primary fill:#007bff,stroke:#0056b3,color:#fff
classDef secondary fill:#6c757d,stroke:#545b62,color:#fff
classDef success fill:#28a745,stroke:#1e7e34,color:#fff
```

## 📱 移动端适配

生成的图表支持响应式设计，在移动设备上也能良好显示。建议：

- PNG 格式：适合文档嵌入
- SVG 格式：适合网页展示，支持缩放
- 高分辨率：设置 DPI 为 300 以上

## 🔄 版本控制

图表文件已纳入版本控制，修改时请：

1. 更新对应的 `.mmd` 文件
2. 重新生成图片文件
3. 更新相关文档中的图片引用
4. 提交所有变更

## 📚 相关文档

- [技术栈总结](../TECHNOLOGY_STACK_SUMMARY.md)
- [FluentMigrator详细指南](../FLUENTMIGRATOR_DETAILED_GUIDE.md)
- [数据库迁移文档](../DATABASE_MIGRATION.md)

## 🆘 故障排除

### 图表无法渲染

1. 检查 Mermaid 语法是否正确
2. 确认节点名称没有特殊字符
3. 验证连接语法是否正确

### 导出图片失败

1. 确认网络连接正常
2. 尝试不同的导出格式
3. 检查浏览器是否支持下载功能

### 样式显示异常

1. 检查 CSS 类名是否正确
2. 确认颜色代码格式正确
3. 验证样式应用语法
