# 测试WorkReportInspection API
$baseUrl = "http://localhost:64922"

Write-Host "=== 测试WorkReportInspection API ===" -ForegroundColor Green

# 测试1: 测试简单列表（不依赖关联数据）
Write-Host "`n1. 测试简单列表..." -ForegroundColor Yellow

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/WorkReportInspection/simple-list" -Method GET
    Write-Host "响应:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

# 测试2: 测试POST方式的列表查询
Write-Host "`n2. 测试POST方式的列表查询..." -ForegroundColor Yellow

$searchBody = @{
    PageIndex = 1
    PageSize = 10
} | ConvertTo-<PERSON>son

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/WorkReportInspection/list" -Method POST -Body $searchBody -ContentType "application/json"
    Write-Host "响应:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试3: 检查数据库中的原始数据
Write-Host "`n3. 测试创建新记录..." -ForegroundColor Yellow

$createBody = @{
    InspectionCode = "TEST-001"
    InspectionName = "测试质检记录"
    InspectionType = "首检"
    Status = "未质检"
    ProductId = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
    ProcessStepId = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
    StationId = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
    TeamId = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
    ReporterId = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
    InspectorId = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
    WorkOrderId = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
    TaskId = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
    ReportedQuantity = 100
    ReportTime = "2025-07-28T10:00:00"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/WorkReportInspection" -Method POST -Body $createBody -ContentType "application/json"
    Write-Host "创建响应:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "创建错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
