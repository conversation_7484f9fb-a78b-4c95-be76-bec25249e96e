using SqlSugar;
using SqlsugarService.Application.Service;
using SqlsugarService.Domain;
using SqlsugarService.Infrastructure.IRepository;
using SqlsugarService.Infrastructure.Repository;
using SqlsugarService.Infrastructure.DbContext;
using SqlsugarService.Application.IService;
using SqlsugarService.Application.IService.Sales;
using SqlsugarService.Application.Service.Sales;
using SqlsugarService.API;
using Microsoft.Extensions.DependencyInjection;
using SqlsugarService.Application.IService.Material;
using SqlsugarService.Application.Service.Material;
using SqlsugarService.Application.IService.Process.ProcessComposition;
using SqlsugarService.Application.Service.Process.ProcessComposition;
using SqlsugarService.Application.IService.Plan;
using SqlsugarService.Application.Service.Plan;
using SqlsugarService.Application.IService.WorkReportInspection;
using SqlsugarService.Application.Service.WorkReportInspection;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddControllers();

// 添加CORS配置
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", builder =>
    {
        builder
            .AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader();
    });
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "SqlsugarService API",
        Version = "v1",
        Description = "基于 SqlSugar 的数据服务微服务 - 提供数据访问和业务逻辑处理"
    });

    // 包含 XML 注释
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath, includeControllerXmlComments: true);
    }
    
    // 确保包含所有控制器
    c.DocInclusionPredicate((name, api) => true);
});
builder.Services.AddScoped<SqlSugarDbContext>();
// 关键：注册ISqlSugarClient，从SqlSugarDbContext中获取实例
builder.Services.AddScoped<ISqlSugarClient>(provider =>
{
    return provider.GetRequiredService<SqlSugarDbContext>().Db;
});
builder.Services.AddScoped(typeof(IBaseRepository<>), typeof(BaseRepository<>));

// 注册质检仓储
builder.Services.AddScoped<IWorkReportInspectionRepository, WorkReportInspectionRepository>();

// 注册数据库初始化服务
builder.Services.AddScoped<SqlsugarService.Infrastructure.Services.DatabaseInitializationService>();

// 注册数据库迁移服务
builder.Services.AddScoped<SqlsugarService.Infrastructure.Services.MigrationService>();

builder.Services.AddScoped<IUserService,UserService>();
builder.Services.AddScoped<ISalesService, SalesService>();
builder.Services.AddScoped<IProductEntityService, ProductEntityService>();
builder.Services.AddScoped<IProductionPlanService, ProductionPlanService>();
builder.Services.AddScoped<IWorkReportInspectionService, WorkReportInspectionService>();

builder.Services.AddAutoMapper(x =>
{
    x.AddProfile(new Mapperfiles());
});
// 注册物料服务
builder.Services.AddScoped<IMaterialService,MaterialService>();
builder.Services.AddScoped<IProcessCompositionService, ProcessCompositionService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "SqlsugarService API v1");
        c.RoutePrefix = "swagger"; // Swagger UI 在 /swagger 路径

        // 优化显示
        c.DefaultModelsExpandDepth(-1);
        c.DefaultModelExpandDepth(2);
        c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.List);
        c.DisplayRequestDuration();
        c.EnableDeepLinking();

        // 自定义样式
        c.DocumentTitle = "SqlsugarService API 文档";
    });
}

// 使用CORS - 必须在UseAuthorization之前
app.UseCors("AllowAll");

app.UseStaticFiles();
app.UseAuthorization();

app.MapControllers();

// 添加根路径重定向到 Swagger (仅在开发环境)
if (app.Environment.IsDevelopment())
{
    app.MapGet("/", () => Results.Redirect("/swagger")).ExcludeFromDescription();
}

app.Run();

