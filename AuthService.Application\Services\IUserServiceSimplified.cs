using AuthService.Domain.Entities;

namespace AuthService.Application.Services;

/// <summary>
/// 用户服务接口（简化版）
/// 定义用户管理的核心业务逻辑
/// </summary>
public interface IUserServiceSimplified
{
    /// <summary>
    /// 获取分页用户列表
    /// </summary>
    /// <param name="page">页码，从1开始</param>
    /// <param name="size">每页大小</param>
    /// <param name="tenantId">租户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户列表和总数</returns>
    Task<(IEnumerable<User> Users, int TotalCount)> GetUsersAsync(
        int page = 1,
        int size = 10,
        string? tenantId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据ID获取用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户信息</returns>
    Task<User?> GetUserByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据用户名获取用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户信息</returns>
    Task<User?> GetUserByUsernameAsync(string username, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据邮箱获取用户
    /// </summary>
    /// <param name="email">邮箱</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户信息</returns>
    Task<User?> GetUserByEmailAsync(string email, CancellationToken cancellationToken = default);

    /// <summary>
    /// 创建用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="email">邮箱</param>
    /// <param name="password">密码</param>
    /// <param name="displayName">显示名称</param>
    /// <param name="createdBy">创建者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的用户</returns>
    Task<User> CreateUserAsync(
        string username,
        string email,
        string password,
        string? displayName = null,
        string? createdBy = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新用户
    /// </summary>
    /// <param name="user">用户实体</param>
    /// <param name="updatedBy">更新者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新后的用户</returns>
    Task<User> UpdateUserAsync(
        User user,
        string? updatedBy = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除用户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="deletedBy">删除者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteUserAsync(
        Guid userId,
        string? deletedBy = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 用户登录验证
    ///
    /// 验证用户名和密码是否正确，用于用户登录功能
    ///
    /// 验证流程：
    /// 1. 根据用户名查找用户
    /// 2. 检查用户状态（是否存在、是否被删除等）
    /// 3. 使用PBKDF2算法验证密码哈希值
    /// 4. 返回验证结果和用户信息
    ///
    /// 密码验证原理：
    /// - 用户输入的明文密码 + 数据库存储的盐值 → PBKDF2算法 → 新哈希值
    /// - 新哈希值与数据库存储的哈希值比较
    /// - 相同则验证成功，不同则验证失败
    ///
    /// 安全特性：
    /// - 使用PBKDF2算法，防止暴力破解
    /// - 每个用户有唯一盐值，防止彩虹表攻击
    /// - 不在日志中记录敏感信息
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="password">用户输入的明文密码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>
    /// 元组包含：
    /// - isValid: 验证是否成功（true=登录成功，false=登录失败）
    /// - user: 验证成功时返回用户信息，失败时为null
    /// </returns>
    Task<(bool isValid, User? user)> ValidateUserLoginAsync(
        string username,
        string password,
        CancellationToken cancellationToken = default);
}
