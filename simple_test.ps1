# 简单API测试
$baseUrl = "http://localhost:64922"

Write-Host "测试状态选项API..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/WorkReportInspection/status-options" -Method GET
    Write-Host "成功! 返回数据:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n测试列表API..." -ForegroundColor Yellow
try {
    $body = '{"pageIndex": 1, "pageSize": 5}'
    $response = Invoke-RestMethod -Uri "$baseUrl/api/WorkReportInspection/list" -Method POST -Body $body -ContentType "application/json"
    Write-Host "成功! 返回数据:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "失败: $($_.Exception.Message)" -ForegroundColor Red
}
