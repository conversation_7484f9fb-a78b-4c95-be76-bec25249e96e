﻿using SqlSugar;
using SqlsugarService.Domain.Common;
using SqlsugarService.Domain.Craftsmanship;
using SqlsugarService.Domain.Materials;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Domain.BOM
{
    /// <summary>
    /// BOM产品物料明细表
    /// </summary>
    [SugarTable("BomItem")]
    public class BomItem : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// BOM主表Id
        /// </summary>
        public Guid BomId { get; set; }
        public BomInfo? Bom { get; set; }

        /// <summary>
        /// 产品物料Id
        /// </summary>
        public Guid MaterialId { get; set; }
        public MaterialEntity? Material { get; set; }

        /// <summary>
        /// 父级明细Id
        /// </summary>
        public Guid? ParentItemId { get; set; }
        public BomItem? ParentItem { get; set; }

        /// <summary>
        /// 用量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 损耗率
        /// </summary>
        public decimal LossRate { get; set; }

        /// <summary>
        /// 投入产出类型
        /// </summary>
        public MaterialRelationType InOutType { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 子级明细列表
        /// </summary>
        public ICollection<BomItem> ChildItems { get; set; } = new List<BomItem>();
    }
}
