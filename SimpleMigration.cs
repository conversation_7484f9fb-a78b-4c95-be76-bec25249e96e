using System;
using System.Data;
using Npgsql;

class Program
{
    static void Main()
    {
        var connectionString = "Host=*************;Port=5432;Database=sqlsugardata;Username=****;Password=****;SearchPath=public";
        
        Console.WriteLine("开始执行数据库迁移...");
        
        try
        {
            using var connection = new NpgsqlConnection(connectionString);
            connection.Open();
            
            Console.WriteLine("数据库连接成功");
            
            // 执行迁移SQL
            var sqls = new[]
            {
                "ALTER TABLE workreportinspectionentity ADD COLUMN IF NOT EXISTS productionplanid UUID NULL;",
                "ALTER TABLE workreportinspectionentity ADD COLUMN IF NOT EXISTS productionorderid UUID NULL;",
                "COMMENT ON COLUMN workreportinspectionentity.productionplanid IS '生产计划Id';",
                "COMMENT ON COLUMN workreportinspectionentity.productionorderid IS '工单Id (生产工单)';"
            };
            
            foreach (var sql in sqls)
            {
                using var cmd = new NpgsqlCommand(sql, connection);
                cmd.ExecuteNonQuery();
                Console.WriteLine($"执行成功: {sql.Substring(0, Math.Min(60, sql.Length))}...");
            }
            
            // 验证结果
            var verifySql = @"
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'workreportinspectionentity' 
                  AND column_name IN ('productionplanid', 'productionorderid')
                ORDER BY column_name;";
                
            using var verifyCmd = new NpgsqlCommand(verifySql, connection);
            using var reader = verifyCmd.ExecuteReader();
            
            Console.WriteLine("\n验证结果:");
            Console.WriteLine("字段名\t\t\t数据类型\t可空");
            Console.WriteLine("------------------------------------------------");
            
            while (reader.Read())
            {
                var columnName = reader.GetString(0);
                var dataType = reader.GetString(1);
                var isNullable = reader.GetString(2);
                Console.WriteLine($"{columnName}\t\t{dataType}\t\t{isNullable}");
            }
            
            Console.WriteLine("\n迁移执行成功！");
            Console.WriteLine("现在可以重新启动API服务测试质检功能");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"迁移执行失败: {ex.Message}");
        }
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
}