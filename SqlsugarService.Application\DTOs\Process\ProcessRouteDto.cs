using System;
using System.ComponentModel.DataAnnotations;

namespace SqlsugarService.Application.DTOs.Process
{
    /// <summary>
    /// 工艺路线数据传输对象
    /// </summary>
    public class ProcessRouteDto
    {
        /// <summary>
        /// 工艺路线编号（必填）
        /// </summary>
        [Required(ErrorMessage = "工艺路线编号不能为空")]
        [StringLength(50, ErrorMessage = "工艺路线编号长度不能超过50个字符")]
        public string ProcessRouteNumber { get; set; } = string.Empty;

        /// <summary>
        /// 工艺路线名称（必填）
        /// </summary>
        [Required(ErrorMessage = "工艺路线名称不能为空")]
        [StringLength(200, ErrorMessage = "工艺路线名称长度不能超过200个字符")]
        public string ProcessRouteName { get; set; } = string.Empty;

        /// <summary>
        /// 状态（启用/禁用，默认启用）
        /// </summary>
        [Required(ErrorMessage = "状态不能为空")]
        [StringLength(10, ErrorMessage = "状态长度不能超过10个字符")]
        public string Status { get; set; } = "启用";

        /// <summary>
        /// 工艺路线说明（可选）
        /// </summary>
        [StringLength(1000, ErrorMessage = "工艺路线说明长度不能超过1000个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 备注（可选）
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remark { get; set; }

        /// <summary>
        /// 是否为当前活动版本（默认为true）
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 版本说明（可选）
        /// </summary>
        [StringLength(500, ErrorMessage = "版本说明长度不能超过500个字符")]
        public string? VersionDescription { get; set; }

        /// <summary>
        /// 上一版本工艺路线Id（可选，用于版本管理）
        /// </summary>
        public Guid? PreviousVersionId { get; set; }
    }

    /// <summary>
    /// 工艺路线列表显示DTO
    /// </summary>
    public class ProcessRouteListDto
    {
        /// <summary>
        /// 工艺路线ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 工艺路线编号
        /// </summary>
        public string ProcessRouteNumber { get; set; } = string.Empty;

        /// <summary>
        /// 工艺路线名称
        /// </summary>
        public string ProcessRouteName { get; set; } = string.Empty;

        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 工艺路线说明
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 是否为当前活动版本
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 版本说明
        /// </summary>
        public string? VersionDescription { get; set; }

        /// <summary>
        /// 上一版本工艺路线Id
        /// </summary>
        public Guid? PreviousVersionId { get; set; }

        ///// <summary>
        ///// 创建时间
        ///// </summary>
        //public DateTime CreatedTime { get; set; }

        ///// <summary>
        ///// 最后更新时间
        ///// </summary>
        //public DateTime LastUpdatedTime { get; set; }

        /// <summary>
        /// 包含的工序数量
        /// </summary>
        public int ProcessStepCount { get; set; }
    }
}