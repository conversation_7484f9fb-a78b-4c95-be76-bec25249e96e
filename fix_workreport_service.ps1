# PowerShell脚本用于修复WorkReportInspectionService中的编译错误

$filePath = "SqlsugarService.Application/Service/WorkReportInspection/WorkReportInspectionService.cs"

# 读取文件内容
$content = Get-Content $filePath -Raw

# 删除所有WorkOrderId的引用
$content = $content -replace "WorkOrderId = entity\.WorkOrderId,\s*", ""
$content = $content -replace "WorkOrderId = createdEntity\.WorkOrderId,\s*", ""
$content = $content -replace "WorkOrderId = updatedEntity\.WorkOrderId,\s*", ""

# 删除所有WorkOrder相关的引用
$content = $content -replace "WorkOrderName = entity\.WorkOrder\?\.OrderName,\s*", ""
$content = $content -replace "WorkOrderCode = entity\.WorkOrder\?\.OrderNumber,\s*", ""
$content = $content -replace "WorkOrderName = createdEntity\.WorkOrder\?\.OrderName,\s*", ""
$content = $content -replace "WorkOrderName = updatedEntity\.WorkOrder\?\.OrderName,\s*", ""

# 删除查询条件中的WorkOrderId
$content = $content -replace "if \(searchDto\.WorkOrderId\.HasValue\)\s*\{\s*query = query\.Where\(x => x\.WorkOrderId == searchDto\.WorkOrderId\.Value\);\s*\}\s*", ""

# 删除Includes中的WorkOrder
$content = $content -replace "\.Includes\(x => x\.WorkOrder\)", ""

# 保存修改后的内容
Set-Content $filePath $content -Encoding UTF8

Write-Host "修复完成！"