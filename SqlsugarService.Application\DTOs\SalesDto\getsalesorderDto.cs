﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.DTOs.SalesDto
{
    /// <summary>
    /// 获取销售单dto
    /// </summary>
    public class getsalesorderDto
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        /// <summary>
        /// 销售单号
        /// </summary>
        public string? SalesCode { get; set; }

        /// <summary>
        /// 销售单名称
        /// </summary>
        public string SalesName { get; set; }
        /// <summary>
        /// 销售人
        /// </summary>
        public string Salesperson { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public string? Remarks { get; set; }
    }
}
