# PowerShell 脚本：测试数据库自动初始化功能
# 用法: .\test-database-init.ps1

Write-Host "🔍 测试数据库自动初始化功能" -ForegroundColor Green

# 1. 检查 PostgreSQL 服务状态
Write-Host "`n1. 检查 PostgreSQL 连接..." -ForegroundColor Yellow

$connectionString = "Host=*************;Port=5432;Database=postgres;Username=****;Password=****;"

try {
    # 使用 psql 测试连接（如果安装了 PostgreSQL 客户端）
    $testConnection = "psql `"$connectionString`" -c `"SELECT version();`""
    Write-Host "测试连接命令: $testConnection" -ForegroundColor Gray
    
    # 或者使用 .NET 测试连接
    Write-Host "尝试使用 .NET 测试连接..." -ForegroundColor Gray
    
} catch {
    Write-Host "❌ 连接测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 检查用户权限
Write-Host "`n2. 检查用户权限..." -ForegroundColor Yellow
Write-Host "请在 PostgreSQL 中执行以下命令检查用户权限：" -ForegroundColor Gray
Write-Host "psql -h ************* -U postgres -d postgres" -ForegroundColor Cyan
Write-Host "SELECT usename, usecreatedb, usesuper FROM pg_user WHERE usename = '****';" -ForegroundColor Cyan

# 3. 如果用户没有权限，提供解决方案
Write-Host "`n3. 如果用户没有创建数据库权限，请执行：" -ForegroundColor Yellow
Write-Host "ALTER USER **** CREATEDB;" -ForegroundColor Cyan

# 4. 手动创建数据库（备选方案）
Write-Host "`n4. 手动创建数据库（如果自动创建失败）：" -ForegroundColor Yellow
Write-Host "CREATE DATABASE authservice OWNER ****;" -ForegroundColor Cyan
Write-Host "GRANT ALL PRIVILEGES ON DATABASE authservice TO ****;" -ForegroundColor Cyan

# 5. 测试应用程序
Write-Host "`n5. 启动应用程序测试：" -ForegroundColor Yellow
Write-Host "cd AuthService.Api" -ForegroundColor Cyan
Write-Host "dotnet run" -ForegroundColor Cyan

Write-Host "`n✅ 如果看到以下日志，说明数据库自动创建成功：" -ForegroundColor Green
Write-Host "- '开始数据库初始化检查...'" -ForegroundColor Gray
Write-Host "- '数据库初始化成功，开始执行迁移...'" -ForegroundColor Gray
Write-Host "- '数据库迁移执行完成'" -ForegroundColor Gray

Write-Host "`n❌ 如果看到以下错误，需要手动处理：" -ForegroundColor Red
Write-Host "- '用户没有创建数据库的权限'" -ForegroundColor Gray
Write-Host "- '无法连接到 PostgreSQL 服务器'" -ForegroundColor Gray

Write-Host "`n📋 完整的权限设置脚本：" -ForegroundColor Blue
Write-Host @"
-- 连接到 PostgreSQL
psql -h ************* -U postgres -d postgres

-- 检查用户
SELECT usename, usecreatedb FROM pg_user WHERE usename = '****';

-- 授予权限
ALTER USER **** CREATEDB;

-- 验证权限
SELECT usename, usecreatedb FROM pg_user WHERE usename = '****';

-- 退出
\q
"@ -ForegroundColor Cyan
