﻿using SqlSugar;
using SqlsugarService.Domain.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Domain.Craftsmanship
{
    /// <summary>
    /// 工艺路线
    /// </summary>
    public class ProcessRouteEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        #region 工艺路线基础信息        

        /// <summary>
        /// 工艺路线编号
        /// </summary>
        public string ProcessRouteNumber { get; set; }

        /// <summary>
        /// 工艺路线名称
        /// </summary>
        public string ProcessRouteName { get; set; }

        /// <summary>
        /// 状态（启用/禁用）
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 工艺路线说明
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        #endregion


        #region 工艺路线版本管理

        /// <summary>
        /// 是否为当前活动版本
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 版本说明
        /// </summary>
        public string? VersionDescription { get; set; }

        /// <summary>
        /// 上一版本工艺路线Id
        /// </summary>
       public Guid? PreviousVersionId { get; set; }
       // public ProcessRouteEntity? PreviousVersion { get; set; }

        /// <summary>
        /// 工序ID列表（逗号分隔）
        /// </summary>
        public string? ProcessStepIds { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        //public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        //public DateTime LastUpdatedTime { get; set; } = DateTime.Now;

        #endregion

        // 导航属性
        /// <summary>
        /// 工艺路线-工序关联列表
        /// </summary>
       // public ICollection<ProcessRouteStep> ProcessRouteSteps { get; set; } = new List<ProcessRouteStep>();

        /// <summary>
        /// 工艺路线-产品关联列表
        /// </summary>
        //public ICollection<ProcessStepProduct> ProcessStepProducts { get; set; } = new List<ProcessStepProduct>();
    }

    /// <summary>
    /// 工艺路线状态枚举
    /// </summary>
    public enum RouteStatus
    {
        /// <summary>
        /// 已启用
        /// </summary>
        Enabled = 1,

        /// <summary>
        /// 已禁用
        /// </summary>
        Disabled = 0
    }
}
