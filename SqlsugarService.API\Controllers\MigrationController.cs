using Microsoft.AspNetCore.Mvc;
using SqlsugarService.Infrastructure.Services;

namespace SqlsugarService.API.Controllers
{
    /// <summary>
    /// 数据库迁移控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class MigrationController : ControllerBase
    {
        private readonly MigrationService _migrationService;

        public MigrationController(MigrationService migrationService)
        {
            _migrationService = migrationService;
        }

        /// <summary>
        /// 执行数据库迁移
        /// </summary>
        /// <returns></returns>
        [HttpPost("run")]
        public async Task<IActionResult> RunMigrations()
        {
            try
            {
                // 检查数据库连接
                var isConnected = await _migrationService.CheckDatabaseConnectionAsync();
                if (!isConnected)
                {
                    return BadRequest(new { message = "数据库连接失败，请检查连接配置" });
                }

                // 执行迁移
                await _migrationService.RunMigrationsAsync();

                return Ok(new 
                { 
                    message = "数据库迁移执行成功",
                    timestamp = DateTime.Now,
                    migrations = new[]
                    {
                        "添加质检表 productionplanid 字段",
                        "添加质检表 productionorderid 字段"
                    }
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new 
                { 
                    message = "数据库迁移执行失败", 
                    error = ex.Message,
                    timestamp = DateTime.Now
                });
            }
        }

        /// <summary>
        /// 回滚数据库迁移
        /// </summary>
        /// <returns></returns>
        [HttpPost("rollback")]
        public async Task<IActionResult> RollbackMigrations()
        {
            try
            {
                await _migrationService.RollbackMigrationsAsync();

                return Ok(new 
                { 
                    message = "数据库迁移回滚成功",
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new 
                { 
                    message = "数据库迁移回滚失败", 
                    error = ex.Message,
                    timestamp = DateTime.Now
                });
            }
        }

        /// <summary>
        /// 检查数据库连接状态
        /// </summary>
        /// <returns></returns>
        [HttpGet("check-connection")]
        public async Task<IActionResult> CheckConnection()
        {
            try
            {
                var isConnected = await _migrationService.CheckDatabaseConnectionAsync();
                
                return Ok(new 
                { 
                    connected = isConnected,
                    message = isConnected ? "数据库连接正常" : "数据库连接失败",
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new 
                { 
                    connected = false,
                    message = "检查数据库连接时发生错误", 
                    error = ex.Message,
                    timestamp = DateTime.Now
                });
            }
        }
    }
}