using SqlSugar;

namespace SqlsugarService.Domain.Common;

/// <summary>
/// 基础实体类，包含所有实体的公共属性
/// 提供审计字段和软删除功能
/// </summary>
public abstract class BaseEntity
{
    /// <summary>
    /// 实体唯一标识符
    /// </summary>
    //public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [SqlSugar.SugarColumn(IsNullable = true)]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建者ID
    /// </summary>
    [SqlSugar.SugarColumn(IsNullable = true)]
    public string? CreatedBy { get; set; }

    /// <summary>
    /// 最后更新者ID
    /// </summary>
    [SqlSugar.SugarColumn(IsNullable = true)]
    public string? UpdatedBy { get; set; }

    /// <summary>
    /// 是否已删除（软删除标记）
    /// </summary>
    /// <remarks>默认为false</remarks>
    [SqlSugar.SugarColumn(DefaultValue = "false")]
    public bool IsDeleted { get; set; } = false;

    /// <summary>
    /// 删除时间
    /// </summary>
    [SqlSugar.SugarColumn(IsNullable = true)]
    public DateTime? DeletedAt { get; set; }=null;

    /// <summary>
    /// 删除者ID
    /// </summary>
    [SqlSugar.SugarColumn(IsNullable = true)]
    public string? DeletedBy { get; set; } = string.Empty;

    /// <summary>
    /// 标记实体为已删除
    /// </summary>
    /// <param name="deletedBy">删除者标识</param>
    public virtual void MarkAsDeleted(string? deletedBy = null)
    {
        IsDeleted = true;
        DeletedAt = DateTime.UtcNow;
        DeletedBy = deletedBy;
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = deletedBy;
    }

    /// <summary>
    /// 更新实体的修改信息
    /// </summary>
    /// <param name="updatedBy">更新者标识</param>
    public virtual void MarkAsUpdated(string? updatedBy = null)
    {
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = updatedBy;
    }
}
