# 质检记录API文档 (前端界面专用)

## 概述
本文档描述了为前端界面特别设计的质检记录API，支持三个主要查询条件：计划编号、产品名称、状态。

## API接口

### 1. 获取质检记录列表

**接口地址：** `POST /api/WorkReportInspection/list`

**功能描述：** 根据前端界面的查询条件获取质检记录分页列表

#### 请求参数

```json
{
  "planNumber": "string",      // 计划编号 (可选，支持模糊查询)
  "productName": "string",     // 产品名称 (可选，支持模糊查询)
  "status": "string",          // 状态 (可选，精确匹配)
  "pageIndex": 1,              // 页码，从1开始 (必填)
  "pageSize": 10               // 每页数量 (必填，1-1000)
}
```

#### 响应数据

```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "data": {
    "data": [
      {
        "id": "guid",
        "inspectionCode": "检验单号",
        "inspectionName": "检验单名称",
        "inspectionType": "检验类型",
        "status": "状态",
        "planNumber": "计划编号",
        "planName": "计划名称",
        "productName": "产品名称",
        "productCode": "产品编号",
        "processStepName": "工序名称",
        "stationName": "站点名称",
        "teamName": "班组名称",
        "reporterName": "报工人员",
        "inspectorName": "检验人员",
        "reportedQuantity": 100,
        "reportTime": "2024-01-01T10:00:00",
        "testedQuantity": 95,
        "qualifiedQuantity": 90,
        "unqualifiedQuantity": 5,
        "inspectionTime": "2024-01-01T11:00:00",
        "overallResult": "合格",
        "remark": "备注信息"
      }
    ],
    "totalCount": 100,
    "totalPage": 10
  }
}
```

### 2. 获取状态选项

**接口地址：** `GET /api/WorkReportInspection/status-options`

**功能描述：** 获取质检状态的可选值列表，用于前端下拉框

#### 响应数据

```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "data": [
    "待质检",
    "质检中",
    "已质检",
    "已完检",
    "不合格",
    "合格"
  ]
}
```

## 查询条件说明

### 1. 计划编号 (planNumber)
- **类型：** 字符串，可选
- **查询方式：** 模糊查询 (LIKE '%value%')
- **数据来源：** ProductionPlan.PlanNumber
- **示例：** "PLAN001", "计划-2024"

### 2. 产品名称 (productName)
- **类型：** 字符串，可选
- **查询方式：** 模糊查询 (LIKE '%value%')
- **数据来源：** Product.MaterialName
- **示例：** "产品A", "零件"

### 3. 状态 (status)
- **类型：** 字符串，可选
- **查询方式：** 精确匹配 (=)
- **数据来源：** WorkReportInspection.Status
- **可选值：** 待质检、质检中、已质检、已完检、不合格、合格

## 使用示例

### 示例1：查询所有记录
```json
{
  "pageIndex": 1,
  "pageSize": 10
}
```

### 示例2：按计划编号查询
```json
{
  "planNumber": "PLAN001",
  "pageIndex": 1,
  "pageSize": 10
}
```

### 示例3：按产品名称查询
```json
{
  "productName": "产品A",
  "pageIndex": 1,
  "pageSize": 10
}
```

### 示例4：按状态查询
```json
{
  "status": "待质检",
  "pageIndex": 1,
  "pageSize": 10
}
```

### 示例5：组合查询
```json
{
  "planNumber": "PLAN",
  "productName": "产品",
  "status": "已质检",
  "pageIndex": 1,
  "pageSize": 20
}
```

## 错误处理

### 常见错误码
- **400：** 参数验证失败
- **404：** 资源不存在
- **500：** 服务器内部错误

### 错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "code": 400,
  "data": null
}
```

## 注意事项

1. **分页参数：** pageIndex从1开始，pageSize范围为1-1000
2. **查询性能：** 建议合理设置pageSize，避免一次查询过多数据
3. **模糊查询：** planNumber和productName支持模糊查询，会自动添加通配符
4. **状态查询：** status为精确匹配，建议先调用status-options接口获取可选值
5. **数据关联：** 查询会自动关联相关表数据，确保返回完整信息

## 测试工具

可以使用提供的Postman集合文件 `Test_WorkReportInspection_Frontend_API.json` 进行接口测试。
