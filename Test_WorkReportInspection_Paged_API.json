{"info": {"name": "质检分页接口测试", "description": "测试质检记录分页查询接口 - 类似工序分页接口的实现方式", "version": "1.0.0"}, "tests": [{"name": "测试1: 基础分页查询", "method": "GET", "url": "/api/WorkReportInspection/list?pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "获取第一页质检记录，每页10条"}, {"name": "测试2: 按状态过滤分页查询", "method": "GET", "url": "/api/WorkReportInspection/list?status=待质检&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "获取状态为'待质检'的质检记录"}, {"name": "测试3: 按状态过滤分页查询 - 已质检", "method": "GET", "url": "/api/WorkReportInspection/list?status=已质检&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "获取状态为'已质检'的质检记录"}, {"name": "测试4: 大页面查询", "method": "GET", "url": "/api/WorkReportInspection/list?pageIndex=1&pageSize=50", "headers": {"Content-Type": "application/json"}, "description": "获取第一页质检记录，每页50条"}, {"name": "测试5: 第二页查询", "method": "GET", "url": "/api/WorkReportInspection/list?pageIndex=2&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "获取第二页质检记录"}, {"name": "测试6: 按合格状态过滤", "method": "GET", "url": "/api/WorkReportInspection/list?status=合格&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "获取状态为'合格'的质检记录"}, {"name": "测试7: 按不合格状态过滤", "method": "GET", "url": "/api/WorkReportInspection/list?status=不合格&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "获取状态为'不合格'的质检记录"}, {"name": "测试8: 参数验证 - 无效页码", "method": "GET", "url": "/api/WorkReportInspection/list?pageIndex=0&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "测试无效页码的错误处理"}, {"name": "测试9: 参数验证 - 无效页面大小", "method": "GET", "url": "/api/WorkReportInspection/list?pageIndex=1&pageSize=1001", "headers": {"Content-Type": "application/json"}, "description": "测试超出限制的页面大小"}, {"name": "测试10: 最小页面大小", "method": "GET", "url": "/api/WorkReportInspection/list?pageIndex=1&pageSize=1", "headers": {"Content-Type": "application/json"}, "description": "测试最小页面大小"}, {"name": "测试11: 获取状态下拉选项", "method": "GET", "url": "/api/WorkReportInspection/status-options", "headers": {"Content-Type": "application/json"}, "description": "获取质检状态下拉选项，用于前端渲染下拉框"}, {"name": "测试12: 全部状态查询（空字符串）", "method": "GET", "url": "/api/WorkReportInspection/list?status=&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "使用空字符串查询所有状态的质检记录"}, {"name": "测试13: 按质检中状态过滤", "method": "GET", "url": "/api/WorkReportInspection/list?status=质检中&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "获取状态为'质检中'的质检记录"}, {"name": "测试14: 按已完检状态过滤", "method": "GET", "url": "/api/WorkReportInspection/list?status=已完检&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "获取状态为'已完检'的质检记录"}, {"name": "测试15: 按检验单号查询", "method": "GET", "url": "/api/WorkReportInspection/list?inspectionCode=QC001&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "根据检验单号进行模糊查询"}, {"name": "测试16: 按检验单名称查询", "method": "GET", "url": "/api/WorkReportInspection/list?inspectionName=首检&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "根据检验单名称进行模糊查询"}, {"name": "测试17: 组合查询 - 检验单号+状态", "method": "GET", "url": "/api/WorkReportInspection/list?inspectionCode=QC001&status=待质检&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "同时按检验单号和状态进行查询"}, {"name": "测试18: 组合查询 - 检验单名称+状态", "method": "GET", "url": "/api/WorkReportInspection/list?inspectionName=首检&status=合格&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "同时按检验单名称和状态进行查询"}, {"name": "测试19: 全条件查询", "method": "GET", "url": "/api/WorkReportInspection/list?inspectionCode=QC001&inspectionName=首检&status=已质检&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "同时使用所有查询条件进行查询"}, {"name": "测试15: 按计划编号查询", "method": "GET", "url": "/api/WorkReportInspection/list?planNumber=PLAN001&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "根据计划编号进行模糊查询"}, {"name": "测试16: 按产品名称查询", "method": "GET", "url": "/api/WorkReportInspection/list?productName=产品A&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "根据产品名称进行模糊查询"}, {"name": "测试17: 组合查询 - 计划编号+状态", "method": "GET", "url": "/api/WorkReportInspection/list?planNumber=PLAN001&status=待质检&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "同时按计划编号和状态进行查询"}, {"name": "测试18: 组合查询 - 产品名称+状态", "method": "GET", "url": "/api/WorkReportInspection/list?productName=产品A&status=合格&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "同时按产品名称和状态进行查询"}, {"name": "测试19: 全条件查询", "method": "GET", "url": "/api/WorkReportInspection/list?planNumber=PLAN&productName=产品&status=已质检&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "同时使用所有查询条件进行查询"}, {"name": "测试15: 按计划编号查询", "method": "GET", "url": "/api/WorkReportInspection/list?planNumber=PLAN001&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "根据计划编号进行模糊查询"}, {"name": "测试16: 按产品名称查询", "method": "GET", "url": "/api/WorkReportInspection/list?productName=产品A&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "根据产品名称进行模糊查询"}, {"name": "测试17: 组合查询 - 计划编号+状态", "method": "GET", "url": "/api/WorkReportInspection/list?planNumber=PLAN001&status=待质检&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "同时按计划编号和状态进行查询"}, {"name": "测试18: 组合查询 - 产品名称+状态", "method": "GET", "url": "/api/WorkReportInspection/list?productName=产品A&status=合格&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "同时按产品名称和状态进行查询"}, {"name": "测试19: 全条件查询", "method": "GET", "url": "/api/WorkReportInspection/list?planNumber=PLAN&productName=产品&status=已质检&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "同时使用所有查询条件进行查询"}, {"name": "测试20: 空条件查询", "method": "GET", "url": "/api/WorkReportInspection/list?planNumber=&productName=&status=&pageIndex=1&pageSize=10", "headers": {"Content-Type": "application/json"}, "description": "所有查询条件为空，查询所有记录"}], "expectedResponse": {"structure": {"code": "number", "message": "string", "data": {"data": "array", "totalCount": "number", "pageIndex": "number", "pageSize": "number", "totalPages": "number"}}, "sampleData": {"code": 200, "message": "操作成功", "data": {"data": [{"id": "guid", "inspectionCode": "string", "inspectionName": "string", "inspectionType": "string", "status": "string", "productId": "guid", "productName": "string", "productCode": "string", "processStepId": "guid", "processStepName": "string", "stationId": "guid", "stationName": "string", "teamId": "guid", "teamName": "string", "reporterId": "guid", "reporterName": "string", "inspectorId": "guid", "inspectorName": "string", "workOrderId": "guid", "workOrderName": "string", "taskId": "guid", "taskName": "string", "reportedQuantity": "number", "reportTime": "datetime", "testedQuantity": "number", "qualifiedQuantity": "number", "unqualifiedQuantity": "number", "inspectionTime": "datetime", "overallResult": "string", "remark": "string", "createdTime": "datetime", "updatedTime": "datetime", "createdBy": "string", "updatedBy": "string"}], "totalCount": 100, "pageIndex": 1, "pageSize": 10, "totalPages": 10}}}}