# PowerShell脚本：执行数据库迁移
# 添加质检表缺失字段

# 数据库连接信息
$Host = "*************"
$Port = "5432"
$Database = "sqlsugardata"
$Username = "kong"
$Password = "kong"

# 设置环境变量
$env:PGPASSWORD = $Password

Write-Host "开始执行数据库迁移..." -ForegroundColor Green

try {
    # 检查psql是否可用
    $psqlVersion = psql --version 2>$null
    if (-not $psqlVersion) {
        Write-Host "错误: 未找到psql命令。请确保PostgreSQL客户端已安装。" -ForegroundColor Red
        Write-Host "你可以从以下地址下载: https://www.postgresql.org/download/" -ForegroundColor Yellow
        exit 1
    }

    Write-Host "PostgreSQL客户端版本: $psqlVersion" -ForegroundColor Cyan

    # 测试数据库连接
    Write-Host "测试数据库连接..." -ForegroundColor Yellow
    $testConnection = psql -h $Host -p $Port -U $Username -d $Database -c "SELECT 1;" 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: 无法连接到数据库" -ForegroundColor Red
        Write-Host $testConnection -ForegroundColor Red
        exit 1
    }
    Write-Host "数据库连接成功!" -ForegroundColor Green

    # 检查表是否存在
    Write-Host "检查质检表是否存在..." -ForegroundColor Yellow
    $tableExists = psql -h $Host -p $Port -U $Username -d $Database -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'workreportinspectionentity');" 2>&1
    if ($tableExists.Trim() -eq "f") {
        Write-Host "警告: 质检表不存在，可能需要先创建表结构" -ForegroundColor Yellow
    } else {
        Write-Host "质检表存在，继续执行迁移..." -ForegroundColor Green
    }

    # 执行迁移SQL
    Write-Host "执行迁移SQL..." -ForegroundColor Yellow
    
    $migrationSQL = @"
-- 添加生产计划ID字段
ALTER TABLE workreportinspectionentity 
ADD COLUMN IF NOT EXISTS productionplanid UUID NULL;

-- 添加生产工单ID字段  
ALTER TABLE workreportinspectionentity 
ADD COLUMN IF NOT EXISTS productionorderid UUID NULL;

-- 添加字段注释
COMMENT ON COLUMN workreportinspectionentity.productionplanid IS '生产计划Id';
COMMENT ON COLUMN workreportinspectionentity.productionorderid IS '工单Id (生产工单)';
"@

    # 执行SQL
    $result = $migrationSQL | psql -h $Host -p $Port -U $Username -d $Database 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: 迁移执行失败" -ForegroundColor Red
        Write-Host $result -ForegroundColor Red
        exit 1
    }

    Write-Host "迁移SQL执行成功!" -ForegroundColor Green

    # 验证迁移结果
    Write-Host "验证迁移结果..." -ForegroundColor Yellow
    $verifySQL = @"
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'workreportinspectionentity' 
  AND column_name IN ('productionplanid', 'productionorderid')
ORDER BY column_name;
"@

    $verifyResult = $verifySQL | psql -h $Host -p $Port -U $Username -d $Database 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "验证结果:" -ForegroundColor Green
        Write-Host $verifyResult -ForegroundColor Cyan
    } else {
        Write-Host "警告: 验证失败，但迁移可能已成功" -ForegroundColor Yellow
        Write-Host $verifyResult -ForegroundColor Yellow
    }

    Write-Host "数据库迁移完成!" -ForegroundColor Green
    Write-Host "现在可以重新启动API服务测试质检功能" -ForegroundColor Yellow

} catch {
    Write-Host "错误: $_" -ForegroundColor Red
    exit 1
} finally {
    # 清除密码环境变量
    Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
}

Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")