# 测试基础API接口
# 验证修复后的接口是否正常工作

$baseUrl = "https://localhost:7001/api/WorkReportInspection"

Write-Host "=== 基础API接口测试 ===" -ForegroundColor Green

# 1. 测试获取状态选项
Write-Host "`n1. 测试获取状态选项..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/status-options" -Method GET -ContentType "application/json" -SkipCertificateCheck
    Write-Host "✅ 状态选项获取成功" -ForegroundColor Green
    Write-Host "返回数据: $($response.data.Count) 个选项"
} catch {
    Write-Host "❌ 状态选项获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 测试获取检验类型选项
Write-Host "`n2. 测试获取检验类型选项..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/inspection-types" -Method GET -ContentType "application/json" -SkipCertificateCheck
    Write-Host "✅ 检验类型选项获取成功" -ForegroundColor Green
    Write-Host "返回数据: $($response.data.Count) 个类型"
} catch {
    Write-Host "❌ 检验类型选项获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 测试获取检测结果选项
Write-Host "`n3. 测试获取检测结果选项..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/result-options" -Method GET -ContentType "application/json" -SkipCertificateCheck
    Write-Host "✅ 检测结果选项获取成功" -ForegroundColor Green
    Write-Host "返回数据: $($response.data.Count) 个结果"
} catch {
    Write-Host "❌ 检测结果选项获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 测试简单列表接口
Write-Host "`n4. 测试简单列表接口..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/simple-list" -Method GET -ContentType "application/json" -SkipCertificateCheck
    Write-Host "✅ 简单列表获取成功" -ForegroundColor Green
    Write-Host "返回记录数: $($response.data.Count)"
} catch {
    Write-Host "❌ 简单列表获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 测试检查关联数据
Write-Host "`n5. 测试检查关联数据..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/check-related-data" -Method GET -ContentType "application/json" -SkipCertificateCheck
    Write-Host "✅ 关联数据检查成功" -ForegroundColor Green
    Write-Host "总记录数: $($response.data.TotalInspectionRecords)"
} catch {
    Write-Host "❌ 关联数据检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 基础API测试完成 ===" -ForegroundColor Green
Write-Host "注意：由于数据库字段缺失，高级搜索功能暂时不可用" -ForegroundColor Yellow
Write-Host "请执行数据库迁移后测试完整功能" -ForegroundColor Cyan