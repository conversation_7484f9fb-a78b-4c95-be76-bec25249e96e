# 质检服务使用指南

## 🎯 当前状态

✅ **项目编译成功** - 所有代码错误已修复  
✅ **基础功能可用** - 核心接口正常工作  
⚠️ **部分功能受限** - 需要数据库迁移后完全恢复  

## 🚀 快速开始

### 1. 启动服务
```bash
# 编译项目
dotnet build EmployeeService.sln

# 启动API服务
dotnet run --project SqlsugarService.API
```

### 2. 访问API文档
启动后访问：`https://localhost:7001/swagger`

### 3. 测试基础功能
```powershell
# 运行快速测试
.\quick_test_available_apis.ps1
```

## 📋 可用接口清单

### ✅ 完全可用的接口

| 接口 | 方法 | 功能 | 状态 |
|------|------|------|------|
| `/status-options` | GET | 获取状态选项 | ✅ 正常 |
| `/inspection-types` | GET | 获取检验类型 | ✅ 正常 |
| `/result-options` | GET | 获取检测结果选项 | ✅ 正常 |
| `/simple-list` | GET | 获取简单列表 | ✅ 正常 |
| `/check-related-data` | GET | 检查关联数据 | ✅ 正常 |
| `/simple-list-paged` | GET | 简单分页查询 | ✅ 正常 |
| `/{id}` | GET | 获取详情 | ✅ 正常 |
| `/` | POST | 创建记录 | ✅ 正常 |
| `/` | PUT | 更新记录 | ✅ 正常 |
| `/{id}` | DELETE | 删除记录 | ✅ 正常 |

### ⚠️ 部分功能受限的接口

| 接口 | 方法 | 功能 | 状态 |
|------|------|------|------|
| `/list` | POST | 高级搜索 | ⚠️ 部分受限 |
| `/statistics` | POST | 统计信息 | ⚠️ 部分受限 |

## 🔍 受限功能详情

由于数据库字段缺失，以下功能暂时不可用：

### 搜索功能受限
- ❌ 计划编号搜索 (`planNumber`)
- ❌ 工单名称搜索 (`workOrderName`)
- ❌ 工单编号搜索 (`workOrderCode`)
- ❌ 任务名称搜索 (`taskName`)
- ❌ 任务编号搜索 (`taskCode`)

### 返回数据受限
- ❌ 计划相关信息 (`planName`, `planNumber`)
- ❌ 工单相关信息 (`workOrderName`, `workOrderCode`)
- ❌ 任务相关信息 (`taskName`, `taskCode`)

## 🔧 恢复完整功能

### 步骤1：执行数据库迁移
```powershell
# 使用自动化脚本
.\execute_field_migration.ps1

# 或手动执行SQL
# 在数据库中执行 fix_database_fields.sql
```

### 步骤2：移除忽略注解
编辑 `SqlsugarService.Domain/QualityInspection/WorkReportInspectionEntity.cs`，移除以下注解：

```csharp
// 移除这些注解
[SugarColumn(IsIgnore = true)] // 暂时忽略此字段，直到数据库迁移完成
```

需要移除注解的字段：
- `ProductionPlanId`
- `ProductionOrderId` 
- `TaskId`
- 相关的导航属性

### 步骤3：重新编译测试
```bash
dotnet build EmployeeService.sln
dotnet run --project SqlsugarService.API
```

## 📊 API使用示例

### 1. 获取状态选项
```bash
GET https://localhost:7001/api/WorkReportInspection/status-options
```

响应：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    { "value": "", "label": "全部状态" },
    { "value": "待质检", "label": "待质检" },
    { "value": "质检中", "label": "质检中" }
  ]
}
```

### 2. 简单分页查询
```bash
GET https://localhost:7001/api/WorkReportInspection/simple-list-paged?pageIndex=1&pageSize=10&status=待质检
```

### 3. 高级搜索（基础功能）
```bash
POST https://localhost:7001/api/WorkReportInspection/list
Content-Type: application/json

{
  "pageIndex": 1,
  "pageSize": 10,
  "inspectionCode": "",
  "inspectionName": "",
  "status": "待质检",
  "inspectionType": "首检"
}
```

## 🧪 测试脚本

### 快速功能测试
```powershell
.\quick_test_available_apis.ps1
```

### 基础API测试
```powershell
.\test_basic_apis.ps1
```

### 完整API测试（迁移后）
```powershell
.\test_workreport_inspection_api.ps1
```

## 🔍 故障排除

### 编译错误
```bash
# 终止进程
taskkill /f /im SqlsugarService.API.exe

# 清理重建
dotnet clean
dotnet build EmployeeService.sln
```

### 接口调用失败
1. 检查服务是否启动
2. 确认端口号（默认7001）
3. 使用 `-SkipCertificateCheck` 参数

### 数据库连接问题
1. 检查连接字符串
2. 确认数据库服务运行
3. 验证数据库权限

## 📝 开发注意事项

### 1. 数据库字段映射
- SqlSugar使用小写字段名
- GUID类型映射为UUID
- 可空字段使用 `Guid?` 类型

### 2. 实体类设计
- 继承 `BaseEntity` 获得审计字段
- 使用 `[Navigate]` 注解定义关联关系
- 计算字段使用 `[SugarColumn(IsIgnore = true)]`

### 3. API设计规范
- 统一使用 `ApiResult<T>` 返回格式
- 分页查询使用 `PageResult<T>`
- 错误处理使用统一的异常处理

## 📚 相关文档

- [完整实现总结](质检服务完整实现总结.md)
- [字段缺失修复方案](质检字段缺失问题完整修复方案.md)
- [API使用示例](WorkReportInspection_API_Modified.md)
- [数据库迁移说明](数据库迁移使用说明.md)

---

**当前版本：v1.0 (基础功能版)**  
**完整版本：v2.0 (需要数据库迁移)**  
**推荐操作：先测试基础功能，再执行迁移恢复完整功能**