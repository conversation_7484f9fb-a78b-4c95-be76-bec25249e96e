# 数据库迁移使用说明

## 📋 **问题描述**

质检服务在运行时出现错误：`column 'productionplanid' does not exist`

这是因为质检实体 `WorkReportInspectionEntity` 中定义了 `ProductionPlanId` 和 `ProductionOrderId` 字段，但数据库表中缺少对应的列。

## 🔧 **解决方案**

我已经创建了完整的数据库迁移解决方案，包括：

### 1. SQL 迁移脚本

- **文件**: `数据库迁移脚本_添加质检表字段.sql`
- **用途**: 可以直接在数据库中执行的 SQL 脚本

### 2. C#迁移类

- **文件**: `SqlsugarService.Infrastructure/Migrations/AddWorkReportInspectionFields.cs`
- **用途**: 程序化执行迁移，支持自动检查和回滚

### 3. 迁移服务

- **文件**: `SqlsugarService.Infrastructure/Services/MigrationService.cs`
- **用途**: 管理所有迁移的执行

### 4. API 控制器

- **文件**: `SqlsugarService.API/Controllers/MigrationController.cs`
- **用途**: 通过 HTTP API 执行迁移

## 🚀 **执行迁移的方法**

### 方法 1：通过 API 执行（推荐）

1. **启动 API 服务**

   ```bash
   cd SqlsugarService.API
   dotnet run
   ```

2. **访问 Swagger 文档**

   ```
   http://localhost:5000/swagger
   ```

3. **执行迁移**

   - 找到 `Migration` 控制器
   - 调用 `POST /api/Migration/run` 接口
   - 查看返回结果确认迁移成功

4. **检查连接**（可选）
   - 调用 `GET /api/Migration/check-connection` 检查数据库连接

### 方法 2：直接执行 SQL 脚本

1. **连接到 PostgreSQL 数据库**

   ```bash
   psql -h ************* -p 5432 -U kong -d sqlsugardata
   ```

2. **执行迁移脚本**

   ```sql
   -- 添加生产计划ID字段
   ALTER TABLE workreportinspectionentity
   ADD COLUMN IF NOT EXISTS productionplanid UUID NULL;

   -- 添加生产工单ID字段
   ALTER TABLE workreportinspectionentity
   ADD COLUMN IF NOT EXISTS productionorderid UUID NULL;

   -- 验证字段是否添加成功
   SELECT column_name, data_type, is_nullable
   FROM information_schema.columns
   WHERE table_name = 'workreportinspectionentity'
     AND column_name IN ('productionplanid', 'productionorderid');
   ```

## 📝 **迁移内容详情**

### 添加的字段

| 字段名              | 类型 | 是否可空 | 描述               |
| ------------------- | ---- | -------- | ------------------ |
| `productionplanid`  | UUID | YES      | 生产计划 Id        |
| `productionorderid` | UUID | YES      | 工单 Id (生产工单) |

### 字段特性

- 两个字段都是可空的（NULL），不会影响现有数据
- 使用 UUID 类型，与其他 ID 字段保持一致
- 添加了适当的注释说明

## ✅ **验证迁移结果**

### 通过 API 验证

调用迁移 API 后，返回结果应该类似：

```json
{
  "message": "数据库迁移执行成功",
  "timestamp": "2025-07-30T...",
  "migrations": [
    "添加质检表 productionplanid 字段",
    "添加质检表 productionorderid 字段"
  ]
}
```

### 通过 SQL 验证

```sql
-- 检查字段是否存在
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'workreportinspectionentity'
  AND column_name IN ('productionplanid', 'productionorderid');
```

预期结果：

```
column_name      | data_type | is_nullable
-----------------|-----------|------------
productionplanid | uuid      | YES
productionorderid| uuid      | YES
```

## 🔄 **回滚迁移**

如果需要回滚迁移，可以：

### 通过 API 回滚

调用 `POST /api/Migration/rollback` 接口

### 通过 SQL 回滚

```sql
-- 删除添加的字段
ALTER TABLE workreportinspectionentity
DROP COLUMN IF EXISTS productionplanid;

ALTER TABLE workreportinspectionentity
DROP COLUMN IF EXISTS productionorderid;
```

## 🎯 **迁移后的效果**

迁移完成后：

- ✅ 质检服务 API 将正常工作
- ✅ 不再出现 `column does not exist` 错误
- ✅ 支持生产计划和工单的关联功能
- ✅ 现有数据不受影响

## 🚨 **注意事项**

1. **备份数据**: 执行迁移前建议备份数据库
2. **测试环境**: 建议先在测试环境执行迁移
3. **权限检查**: 确保数据库用户有 ALTER TABLE 权限
4. **连接检查**: 确保数据库连接正常

## 📞 **故障排除**

### 常见问题

1. **权限不足**

   ```
   ERROR: permission denied for table workreportinspectionentity
   ```

   **解决**: 确保数据库用户有 ALTER 权限

2. **表不存在**

   ```
   ERROR: relation "workreportinspectionentity" does not exist
   ```

   **解决**: 检查表名是否正确，或先创建表

3. **连接失败**
   ```
   ERROR: could not connect to server
   ```
   **解决**: 检查数据库连接配置

---

**创建时间**: 2025-07-30  
**状态**: 准备执行  
**下一步**: 选择合适的方法执行迁移
