# FluentMigrator 详细应用指南

## 📋 概述

本项目使用 **FluentMigrator** 作为数据库迁移框架，实现数据库结构的版本控制和自动化管理。FluentMigrator 是一个基于 .NET 的数据库迁移框架，支持多种数据库系统，本项目专门针对 PostgreSQL 进行了配置。

## 🏗️ 架构设计

### 核心组件

```
FluentMigrator 架构
├── ServiceCollectionExtensions.cs    # 依赖注入配置
├── MigrationRunner.cs                # 迁移执行器
├── DatabaseInitializer.cs            # 数据库初始化器
├── Migrations/                       # 迁移文件目录
│   ├── 001_CreateUsersTable.cs      # 用户表迁移
│   └── 002_CreateDynamicApiEndpointsTable.cs  # 动态API端点表迁移
└── scripts/                          # 手动执行脚本
    ├── migrate-database.ps1         # Windows PowerShell脚本
    └── migrate-database.sh          # Linux/macOS Bash脚本
```

## 🔧 配置详解

### 1. 依赖注入配置 (ServiceCollectionExtensions.cs)

```csharp
// 配置FluentMigrator核心服务
services.AddFluentMigratorCore()
    .ConfigureRunner(rb => rb
        // 指定使用PostgreSQL数据库提供程序
        .AddPostgres()
        // 设置全局数据库连接字符串，所有迁移都将使用此连接
        .WithGlobalConnectionString(connectionString)
        // 扫描当前程序集中的所有迁移类
        // 这会自动发现所有标记了[Migration]特性的类
        .ScanIn(typeof(ServiceCollectionExtensions).Assembly).For.Migrations())
    // 添加FluentMigrator控制台日志记录
    // 这样可以在控制台看到迁移执行的详细信息
    .AddLogging(lb => lb.AddFluentMigratorConsole());
```

**关键配置说明：**

- `AddPostgres()`: 指定 PostgreSQL 数据库提供程序
- `WithGlobalConnectionString()`: 设置全局连接字符串
- `ScanIn().For.Migrations()`: 自动扫描迁移类
- `AddFluentMigratorConsole()`: 启用控制台日志

### 2. 迁移执行器 (MigrationRunner.cs)

```csharp
public async Task<bool> MigrateAsync()
{
    // 1. 数据库初始化（检查连接、创建数据库、验证权限）
    var initResult = await dbInitializer.InitializeAsync();

    // 2. 获取FluentMigrator的迁移运行器
    var runner = scope.ServiceProvider.GetRequiredService<IMigrationRunner>();

    // 3. 执行所有待执行的迁移
    // MigrateUp()会：
    // - 检查数据库中的VersionInfo表，了解已执行的迁移
    // - 找出所有尚未执行的迁移（版本号大于当前数据库版本的迁移）
    // - 按版本号顺序执行这些迁移
    // - 在VersionInfo表中记录每个成功执行的迁移
    runner.MigrateUp();
}
```

## 📊 版本控制机制

### VersionInfo 表结构

FluentMigrator 自动创建 `VersionInfo` 表来跟踪迁移状态：

```sql
CREATE TABLE "VersionInfo" (
    "Version" BIGINT NOT NULL PRIMARY KEY,
    "AppliedOn" TIMESTAMP NOT NULL,
    "Description" VARCHAR(1024)
);
```

### 版本号规则

- **格式**: `YYYYMMDDNNN` (年月日 + 3 位序号)
- **示例**:
  - `20241214001` - 2024 年 12 月 14 日第 1 个迁移
  - `20241214002` - 2024 年 12 月 14 日第 2 个迁移

### 执行逻辑

```mermaid
graph TD
    A[应用启动] --> B[调用 MigrateAsync]
    B --> C[数据库初始化]
    C --> D[检查 VersionInfo 表]
    D --> E{有新迁移?}
    E -->|是| F[按版本号排序]
    F --> G[逐个执行迁移]
    G --> H[更新 VersionInfo]
    E -->|否| I[跳过迁移]
    H --> J[完成]
    I --> J
```

## 🗃️ 迁移文件详解

### 1. 用户表迁移 (001_CreateUsersTable.cs)

```csharp
[Migration(20241214001)] // 版本号标记
public class CreateUsersTable : Migration
{
    public override void Up()
    {
        Create.Table("Users")
            // 基础标识字段
            .WithColumn("Id").AsGuid().PrimaryKey("PK_Users")
            .WithColumn("Username").AsString(50).NotNullable().Unique("UQ_Users_Username")
            // ... 更多字段定义

        // 创建索引提高查询性能
        Create.Index("IX_Users_Email")
            .OnTable("Users")
            .OnColumn("Email")
            .Ascending();
    }

    public override void Down()
    {
        Delete.Table("Users"); // 回滚操作
    }
}
```

**字段分类：**

- **基础字段**: Id, Username, Email, PasswordHash, PasswordSalt
- **用户信息**: DisplayName, AvatarUrl, PhoneNumber
- **验证状态**: IsEmailVerified, IsPhoneVerified, IsActive, IsLocked
- **权限字段**: Roles, Permissions (JSON 格式)
- **审计字段**: CreatedAt, UpdatedAt, CreatedBy, UpdatedBy
- **软删除**: IsDeleted, DeletedAt, DeletedBy

### 2. 动态 API 端点表迁移 (002_CreateDynamicApiEndpointsTable.cs)

```csharp
[Migration(20241214002)]
public class CreateDynamicApiEndpointsTable : Migration
{
    public override void Up()
    {
        Create.Table("DynamicApiEndpoints")
            // HTTP配置字段
            .WithColumn("Method").AsInt32().NotNullable() // HttpMethod枚举值
            .WithColumn("PathTemplate").AsString(500).NotNullable()
            .WithColumn("TargetService").AsString(100).Nullable()
            // ... 更多配置字段

        // 唯一约束：防止重复路由
        Create.Index("UQ_DynamicApiEndpoints_TenantId_PathTemplate_Method")
            .OnTable("DynamicApiEndpoints")
            .OnColumn("TenantId").Ascending()
            .OnColumn("PathTemplate").Ascending()
            .OnColumn("Method").Ascending()
            .WithOptions().Unique();
    }
}
```

**功能特性：**

- **动态路由**: 支持运行时配置 API 端点
- **权限控制**: RequireAuthentication, RequiredRoles, RequiredPermissions
- **性能配置**: RateLimitPerMinute, TimeoutSeconds, CacheSeconds
- **请求转换**: RequestTransformation, ResponseTransformation
- **多租户支持**: TenantId 字段实现租户隔离

## 🚀 执行方式

### 1. 自动执行（推荐）

应用启动时自动执行：

```csharp
// Program.cs
using (var scope = app.Services.CreateScope())
{
    var migrationRunner = scope.ServiceProvider.GetRequiredService<MigrationRunner>();
    var success = await migrationRunner.MigrateAsync();
}
```

### 2. 手动执行

使用提供的脚本：

```powershell
# Windows
.\scripts\migrate-database.ps1 -Action up

# Linux/macOS
./scripts/migrate-database.sh -a up
```

### 3. 命令行工具

直接使用 FluentMigrator CLI：

```bash
dotnet fm migrate -p postgres -c "connection_string" -a "assembly_path"
```

## 📈 性能优化

### 索引策略

1. **单列索引**: 用于简单查询

   ```csharp
   Create.Index("IX_Users_Email").OnTable("Users").OnColumn("Email").Ascending();
   ```

2. **复合索引**: 用于复杂查询

   ```csharp
   Create.Index("IX_Users_TenantId_IsActive_IsDeleted")
       .OnTable("Users")
       .OnColumn("TenantId").Ascending()
       .OnColumn("IsActive").Ascending()
       .OnColumn("IsDeleted").Ascending();
   ```

3. **唯一约束**: 防止数据重复
   ```csharp
   .WithOptions().Unique();
   ```

### 查询优化建议

- **多租户查询**: 始终包含 TenantId 条件
- **软删除查询**: 添加 IsDeleted = false 条件
- **状态查询**: 利用 IsActive, IsEnabled 等状态字段
- **时间范围查询**: 使用 CreatedAt, UpdatedAt 等时间字段的索引

## 🛠️ 最佳实践

### 1. 迁移文件命名

- 使用描述性名称: `CreateUsersTable`, `AddIndexToUsers`
- 版本号递增: 确保新迁移的版本号大于已有迁移

### 2. 字段设计

- **主键**: 统一使用 GUID 类型
- **字符串长度**: 根据实际需求设置合理长度
- **默认值**: 为必填字段设置合理默认值
- **可空性**: 明确字段是否可为空

### 3. 索引设计

- **查询频率**: 为经常查询的字段创建索引
- **复合索引**: 考虑多字段组合查询的场景
- **索引顺序**: 将选择性高的字段放在前面

### 4. 回滚策略

- **谨慎使用**: 回滚会删除数据，生产环境需特别小心
- **备份数据**: 执行回滚前务必备份重要数据
- **测试验证**: 在测试环境充分验证回滚操作

## 🔍 故障排除

### 常见问题

1. **连接失败**: 检查 PostgreSQL 服务状态和连接字符串
2. **权限不足**: 确保用户具有 CREATEDB 权限
3. **迁移冲突**: 检查版本号是否重复
4. **索引冲突**: 确保索引名称唯一

### 调试技巧

1. **启用详细日志**: 使用 FluentMigratorConsole 日志
2. **手动验证**: 使用 psql 连接数据库检查结构
3. **分步执行**: 逐个执行迁移文件排查问题

## 🎯 实际应用示例

### 创建新迁移的完整流程

1. **创建迁移文件**

```csharp
[Migration(20241215001)] // 新的版本号
public class AddUserProfileTable : Migration
{
    public override void Up()
    {
        Create.Table("UserProfiles")
            .WithColumn("Id").AsGuid().PrimaryKey()
            .WithColumn("UserId").AsGuid().NotNullable()
            .WithColumn("Bio").AsString(1000).Nullable()
            .WithColumn("Website").AsString(255).Nullable()
            .WithColumn("Location").AsString(100).Nullable()
            .WithColumn("CreatedAt").AsDateTime().NotNullable().WithDefaultValue(SystemMethods.CurrentUTCDateTime);

        // 创建外键关系
        Create.ForeignKey("FK_UserProfiles_Users")
            .FromTable("UserProfiles").ForeignColumn("UserId")
            .ToTable("Users").PrimaryColumn("Id")
            .OnDelete(Rule.Cascade);

        // 创建索引
        Create.Index("IX_UserProfiles_UserId")
            .OnTable("UserProfiles")
            .OnColumn("UserId")
            .Unique();
    }

    public override void Down()
    {
        Delete.Table("UserProfiles");
    }
}
```

2. **测试迁移**

```bash
# 验证迁移语法
dotnet build

# 执行迁移
dotnet run

# 检查数据库结构
psql -h localhost -U postgres -d authservice -c "\dt"
```

### 数据迁移示例

```csharp
[Migration(20241215002)]
public class MigrateUserRolesData : Migration
{
    public override void Up()
    {
        // 更新现有数据
        Execute.Sql(@"
            UPDATE ""Users""
            SET ""Roles"" = '[""User""]'
            WHERE ""Roles"" = '[]' AND ""IsActive"" = true
        ");

        // 添加新的默认数据
        Insert.IntoTable("Users")
            .Row(new
            {
                Id = Guid.NewGuid(),
                Username = "admin",
                Email = "<EMAIL>",
                PasswordHash = "hashed_password",
                PasswordSalt = "salt",
                Roles = "[\"Admin\"]",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            });
    }

    public override void Down()
    {
        Execute.Sql(@"
            DELETE FROM ""Users""
            WHERE ""Username"" = 'admin'
        ");
    }
}
```

## 🔄 版本控制最佳实践

### 团队协作规范

1. **版本号分配**

   - 每个开发者分配特定的序号范围
   - 开发者 A: 001-099, 开发者 B: 100-199
   - 避免版本号冲突

2. **代码审查**

   - 所有迁移文件必须经过代码审查
   - 重点检查：字段类型、索引设计、约束条件
   - 验证回滚操作的正确性

3. **环境同步**

   ```bash
   # 开发环境
   dotnet run --environment Development

   # 测试环境
   dotnet run --environment Testing

   # 生产环境
   dotnet run --environment Production
   ```

### 生产环境部署策略

1. **备份策略**

   ```bash
   # 部署前备份
   pg_dump -h localhost -U postgres authservice > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **分阶段部署**

   - 先在测试环境验证迁移
   - 生产环境维护窗口执行
   - 监控迁移执行时间和资源使用

3. **回滚准备**

   ```bash
   # 记录当前版本
   dotnet fm list migrations -p postgres -c "connection_string" -a "assembly_path"

   # 准备回滚命令
   dotnet fm rollback -p postgres -c "connection_string" -a "assembly_path" --version 20241214001
   ```

## 📊 监控和维护

### 性能监控

1. **迁移执行时间**

   - 记录每次迁移的执行时间
   - 识别耗时较长的迁移操作
   - 优化大表的结构变更

2. **数据库大小监控**

   ```sql
   -- 查看表大小
   SELECT
       schemaname,
       tablename,
       pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
   FROM pg_tables
   WHERE schemaname = 'public'
   ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
   ```

3. **索引使用情况**
   ```sql
   -- 查看索引使用统计
   SELECT
       schemaname,
       tablename,
       indexname,
       idx_scan,
       idx_tup_read,
       idx_tup_fetch
   FROM pg_stat_user_indexes
   ORDER BY idx_scan DESC;
   ```

### 维护任务

1. **定期清理**

   - 清理过期的软删除数据
   - 重建碎片化的索引
   - 更新表统计信息

2. **版本信息管理**

   ```sql
   -- 查看迁移历史
   SELECT * FROM "VersionInfo" ORDER BY "Version" DESC;

   -- 清理异常的版本记录（谨慎操作）
   DELETE FROM "VersionInfo" WHERE "Version" = 20241215999;
   ```

## 🚨 注意事项和限制

### 重要警告

1. **生产环境操作**

   - 永远不要在生产环境直接修改迁移文件
   - 已执行的迁移不应该被修改
   - 回滚操作会导致数据丢失

2. **数据安全**

   - 大表结构变更可能导致长时间锁表
   - 删除列操作不可逆，务必备份数据
   - 外键约束变更需要考虑数据完整性

3. **性能影响**
   - 索引创建可能影响写入性能
   - 大量数据迁移需要分批处理
   - 考虑在低峰期执行重要迁移

### 技术限制

1. **FluentMigrator 限制**

   - 不支持某些 PostgreSQL 特有功能
   - 复杂的数据转换需要使用原生 SQL
   - 跨数据库兼容性有限

2. **PostgreSQL 特性**
   - 某些操作需要超级用户权限
   - 并发迁移可能导致死锁
   - 大对象(LOB)处理需要特殊考虑

## 📚 扩展阅读

- [FluentMigrator 官方文档](https://fluentmigrator.github.io/)
- [PostgreSQL 索引优化指南](https://www.postgresql.org/docs/current/indexes.html)
- [数据库迁移最佳实践](https://martinfowler.com/articles/evodb.html)
- [PostgreSQL 性能调优](https://wiki.postgresql.org/wiki/Performance_Optimization)
- [.NET 数据访问模式](https://docs.microsoft.com/en-us/dotnet/architecture/microservices/microservice-ddd-cqrs-patterns/)
