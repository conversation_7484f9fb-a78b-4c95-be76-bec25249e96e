using SqlSugar;
using SqlsugarService.Domain.Common;
using SqlsugarService.Domain.Craftsmanship;
using SqlsugarService.Domain.Materials;
using SqlsugarService.Domain.Plan;
using SqlsugarService.Domain.Station;
using SqlsugarService.Domain.Team;
using System;

namespace SqlsugarService.Domain.QualityInspection
{
    /// <summary>
    /// 报工质检实体类
    /// </summary>
    public class WorkReportInspectionEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; } = Guid.NewGuid();

        #region 基础信息
        /// <summary>
        /// 检验单号
        /// </summary>
        public string InspectionCode { get; set; }

        /// <summary>
        /// 检验单名称
        /// </summary>
        public string InspectionName { get; set; }

        /// <summary>
        /// 检验类型 (如：首检, 巡检, 末检)
        /// </summary>
        public string InspectionType { get; set; }

        /// <summary>
        /// 状态 (如: 未质检, 已质检, 已完检)
        /// </summary>
        public string Status { get; set; }
        #endregion

        #region 关联外键
        /// <summary>
        /// 产品Id
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// 工序Id
        /// </summary>
        public Guid ProcessStepId { get; set; }

        /// <summary>
        /// 站点Id
        /// </summary>
        public Guid StationId { get; set; }

        /// <summary>
        /// 班组Id
        /// </summary>
        public Guid TeamId { get; set; }

        /// <summary>
        /// 报工人员Id
        /// </summary>
        public Guid ReporterId { get; set; }

        /// <summary>
        /// 检验人员Id
        /// </summary>
        public Guid? InspectorId { get; set; }

        /// <summary>
        /// 生产计划Id (暂时忽略，数据库表结构问题)
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Guid? ProductionPlanId { get; set; }

        /// <summary>
        /// 工单Id (生产工单) - 暂时忽略，数据库表结构问题
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Guid? ProductionOrderId { get; set; }

        /// <summary>
        /// 任务Id (工单任务) - 暂时忽略，数据库表结构问题
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Guid? TaskId { get; set; }
        #endregion

        #region 导航属性
        /// <summary>
        /// 产品信息
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(ProductId))]
        public ProductEntity? Product { get; set; }

        /// <summary>
        /// 工序信息
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(ProcessStepId))]
        public ProcessStep? ProcessStep { get; set; }

        /// <summary>
        /// 站点信息
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(StationId))]
        public StationEntity? Station { get; set; }

        /// <summary>
        /// 班组信息
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(TeamId))]
        public TeamEntity? Team { get; set; }

        /// <summary>
        /// 报工人员信息
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(ReporterId))]
        public Users? Reporter { get; set; }

        /// <summary>
        /// 检验人员信息
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(InspectorId))]
        public Users? Inspector { get; set; }

        /// <summary>
        /// 生产计划信息
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(ProductionPlanId))]
        public ProductionPlan? ProductionPlan { get; set; }

        /// <summary>
        /// 生产工单信息
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(ProductionOrderId))]
        public ProductionOrder? ProductionOrder { get; set; }

        /// <summary>
        /// 工单任务信息
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(TaskId))]
        public WorkOrderTaskEntity? Task { get; set; }
        #endregion

        #region 报工信息
        /// <summary>
        /// 报工数量
        /// </summary>
        public int ReportedQuantity { get; set; }

        /// <summary>
        /// 报工时间
        /// </summary>
        public DateTime ReportTime { get; set; }
        #endregion

        #region 质检信息
        /// <summary>
        /// 检验时间
        /// </summary>
        public DateTime? InspectionTime { get; set; }

        /// <summary>
        /// 检验部门
        /// </summary>
        public string? InspectionDepartment { get; set; }

        /// <summary>
        /// 检测数量
        /// </summary>
        public int? TestedQuantity { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        public int? QualifiedQuantity { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        public int? UnqualifiedQuantity { get; set; }

        /// <summary>
        /// 合格率 (计算字段，百分比)
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public decimal? QualificationRate
        {
            get
            {
                if (TestedQuantity.HasValue && TestedQuantity.Value > 0 && QualifiedQuantity.HasValue)
                {
                    return Math.Round((decimal)QualifiedQuantity.Value / TestedQuantity.Value * 100, 2);
                }
                return null;
            }
        }

        /// <summary>
        /// 检测结果 (如: 合格, 不合格)
        /// </summary>
        public string? OverallResult { get; set; }
        #endregion

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }
}