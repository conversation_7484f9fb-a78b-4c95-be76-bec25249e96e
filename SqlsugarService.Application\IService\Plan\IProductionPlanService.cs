﻿using SqlsugarService.Application.DTOs.PlanDto;
using SqlsugarService.Application.Until;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.IService.Plan
{
    public interface IProductionPlanService
    {
        Task<ApiResult> AddProductionPlan(InsertupdateproductionplanDto dto);
        Task<ApiResult> UpdateProductionPlan(InsertupdateproductionplanDto dto);
        Task<ApiResult<PageResult<List<GetproductionplanDto>>>> GetProductionPlanList(GetproductionplanSearchDto seach);
        Task<ApiResult> DeleteProductionPlan(Guid id);
    }
}
