using SqlsugarService.Application.DTOs.WorkReportInspectionDto;
using SqlsugarService.Application.IService.WorkReportInspection;
using SqlsugarService.Application.Until;
using SqlsugarService.Domain.QualityInspection;
using SqlsugarService.Infrastructure.IRepository;
using SqlSugar;

namespace SqlsugarService.Application.Service.WorkReportInspection
{
    /// <summary>
    /// 质检服务实现
    /// </summary>
    public class WorkReportInspectionService : IWorkReportInspectionService
    {
        private readonly IWorkReportInspectionRepository _workReportInspectionRepository;

        public WorkReportInspectionService(IWorkReportInspectionRepository workReportInspectionRepository)
        {
            _workReportInspectionRepository = workReportInspectionRepository;
        }

        /// <summary>
        /// 分页获取质检记录列表
        /// </summary>
        /// <param name="inspectionCode">检验单号（可选）</param>
        /// <param name="inspectionName">检验单名称（可选）</param>
        /// <param name="status">状态过滤（可选）</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页数量</param>
        /// <returns>分页的质检记录列表</returns>
        public async Task<ApiResult<PageResult<List<GetWorkReportInspectionDto>>>> GetPagedListAsync(string? inspectionCode, string? inspectionName, string? status, int pageIndex, int pageSize)
        {
            try
            {
                // 构建查询条件
                var query = _workReportInspectionRepository.AsQueryable();

                // 包含关联数据
                query = query.Includes(x => x.Product)
                           .Includes(x => x.ProcessStep)
                           .Includes(x => x.Station)
                           .Includes(x => x.Team)
                           .Includes(x => x.Reporter)
                           .Includes(x => x.Inspector)
                           .Includes(x => x.Task)
                           .Includes(x => x.ProductionPlan)
                           .Includes(x => x.ProductionOrder);

                // 检验单号过滤
                if (!string.IsNullOrEmpty(inspectionCode?.Trim()))
                {
                    query = query.Where(x => x.InspectionCode.Contains(inspectionCode.Trim()));
                }

                // 检验单名称过滤
                if (!string.IsNullOrEmpty(inspectionName?.Trim()))
                {
                    query = query.Where(x => x.InspectionName.Contains(inspectionName.Trim()));
                }

                // 状态过滤
                if (!string.IsNullOrEmpty(status?.Trim()))
                {
                    query = query.Where(x => x.Status == status.Trim());
                }

                // 分页查询
                var totalCount = await query.CountAsync();
                var entities = await query
                    .OrderByDescending(x => x.ReportTime)
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // 转换为DTO
                var dtos = entities.Select(entity => new GetWorkReportInspectionDto
                {
                    Id = entity.Id,
                    InspectionCode = entity.InspectionCode,
                    InspectionName = entity.InspectionName,
                    InspectionType = entity.InspectionType,
                    Status = entity.Status,

                    // 外键
                    ProductId = entity.ProductId,
                    ProcessStepId = entity.ProcessStepId,
                    StationId = entity.StationId,
                    TeamId = entity.TeamId,
                    ReporterId = entity.ReporterId,
                    InspectorId = entity.InspectorId,
                    TaskId = entity.TaskId,

                    // 关联信息
                    ProductName = entity.Product?.MaterialName,
                    ProductCode = entity.Product?.MaterialNumber,
                    ProcessStepName = entity.ProcessStep?.ProcessStepName,
                    StationName = entity.Station?.StationName,
                    TeamName = entity.Team?.TeamName,
                    ReporterName = entity.Reporter?.DisplayName,
                    InspectorName = entity.Inspector?.DisplayName,
                    TaskName = entity.Task?.TaskName,
                    TaskCode = entity.Task?.TaskNumber,

                    // 报工和质检信息
                    ReportedQuantity = entity.ReportedQuantity,
                    ReportTime = entity.ReportTime,
                    TestedQuantity = entity.TestedQuantity,
                    QualifiedQuantity = entity.QualifiedQuantity,
                    UnqualifiedQuantity = entity.UnqualifiedQuantity,
                    InspectionTime = entity.InspectionTime,
                    OverallResult = entity.OverallResult,
                    Remark = entity.Remark
                }).ToList();

                var pageResult = new PageResult<List<GetWorkReportInspectionDto>>
                {
                    Data = dtos,
                    TotalCount = totalCount,
                    TotalPage = (int)Math.Ceiling((double)totalCount / pageSize)
                };

                return ApiResult<PageResult<List<GetWorkReportInspectionDto>>>.Success(pageResult, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<PageResult<List<GetWorkReportInspectionDto>>>.Fail(
                    $"获取质检记录分页列表失败: {ex.Message}", 
                    ResultCode.Error);
            }
        }

        /// <summary>
        /// 根据ID获取质检记录详情
        /// </summary>
        /// <param name="id">质检记录ID</param>
        /// <returns>质检记录详情</returns>
        public async Task<ApiResult<GetWorkReportInspectionDto>> GetByIdAsync(Guid id)
        {
            try
            {
                var entity = await _workReportInspectionRepository.AsQueryable()
                    .Includes(x => x.Product)
                    .Includes(x => x.ProcessStep)
                    .Includes(x => x.Station)
                    .Includes(x => x.Team)
                    .Includes(x => x.Reporter)
                    .Includes(x => x.Inspector)
                    .Includes(x => x.Task)
                    .FirstAsync(x => x.Id == id);

                if (entity == null)
                {
                    return ApiResult<GetWorkReportInspectionDto>.Fail("质检记录不存在", ResultCode.NotFound);
                }

                var dto = new GetWorkReportInspectionDto
                {
                    Id = entity.Id,
                    InspectionCode = entity.InspectionCode,
                    InspectionName = entity.InspectionName,
                    InspectionType = entity.InspectionType,
                    Status = entity.Status,

                    // 外键
                    ProductId = entity.ProductId,
                    ProcessStepId = entity.ProcessStepId,
                    StationId = entity.StationId,
                    TeamId = entity.TeamId,
                    ReporterId = entity.ReporterId,
                    InspectorId = entity.InspectorId,
                    TaskId = entity.TaskId,

                    // 关联信息
                    ProductName = entity.Product?.MaterialName,
                    ProductCode = entity.Product?.MaterialNumber,
                    ProcessStepName = entity.ProcessStep?.ProcessStepName,
                    StationName = entity.Station?.StationName,
                    TeamName = entity.Team?.TeamName,
                    ReporterName = entity.Reporter?.DisplayName,
                    InspectorName = entity.Inspector?.DisplayName,
                    TaskName = entity.Task?.TaskName,
                    TaskCode = entity.Task?.TaskNumber,

                    // 报工和质检信息
                    ReportedQuantity = entity.ReportedQuantity,
                    ReportTime = entity.ReportTime,
                    TestedQuantity = entity.TestedQuantity,
                    QualifiedQuantity = entity.QualifiedQuantity,
                    UnqualifiedQuantity = entity.UnqualifiedQuantity,
                    InspectionTime = entity.InspectionTime,
                    OverallResult = entity.OverallResult,
                    Remark = entity.Remark
                };

                return ApiResult<GetWorkReportInspectionDto>.Success(dto, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<GetWorkReportInspectionDto>.Fail(
                    $"获取质检记录详情失败: {ex.Message}", 
                    ResultCode.Error);
            }
        }

        /// <summary>
        /// 创建质检记录
        /// </summary>
        /// <param name="dto">创建质检记录DTO</param>
        /// <returns>创建结果</returns>
        public async Task<ApiResult<GetWorkReportInspectionDto>> CreateAsync(CreateWorkReportInspectionDto dto)
        {
            try
            {
                var entity = new WorkReportInspectionEntity
                {
                    InspectionCode = dto.InspectionCode,
                    InspectionName = dto.InspectionName,
                    InspectionType = dto.InspectionType,
                    Status = "待质检", // 默认状态

                    // 外键
                    ProductId = dto.ProductId,
                    ProcessStepId = dto.ProcessStepId,
                    StationId = dto.StationId,
                    TeamId = dto.TeamId,
                    ReporterId = dto.ReporterId,
                    InspectorId = dto.InspectorId,
                    TaskId = dto.TaskId,

                    // 报工信息
                    ReportedQuantity = dto.ReportedQuantity,
                    ReportTime = dto.ReportTime,

                    // 质检信息
                    TestedQuantity = dto.TestedQuantity,
                    QualifiedQuantity = dto.QualifiedQuantity,
                    UnqualifiedQuantity = dto.UnqualifiedQuantity,
                    InspectionTime = dto.InspectionTime,
                    OverallResult = dto.OverallResult,
                    Remark = dto.Remark,

                    // 审计字段会由BaseEntity自动处理
                };

                var createdEntity = await _workReportInspectionRepository.InsertReturnEntityAsync(entity);

                // 重新查询以获取关联数据
                var result = await GetByIdAsync(createdEntity.Id);
                return result;
            }
            catch (Exception ex)
            {
                return ApiResult<GetWorkReportInspectionDto>.Fail(
                    $"创建质检记录失败: {ex.Message}", 
                    ResultCode.Error);
            }
        }

        /// <summary>
        /// 更新质检记录
        /// </summary>
        /// <param name="dto">更新质检记录DTO</param>
        /// <returns>更新结果</returns>
        public async Task<ApiResult<GetWorkReportInspectionDto>> UpdateAsync(UpdateWorkReportInspectionDto dto)
        {
            try
            {
                var entity = await _workReportInspectionRepository.GetByIdAsync(dto.Id);
                if (entity == null)
                {
                    return ApiResult<GetWorkReportInspectionDto>.Fail("质检记录不存在", ResultCode.NotFound);
                }

                // 更新字段
                if (!string.IsNullOrEmpty(dto.InspectionName))
                    entity.InspectionName = dto.InspectionName;
                if (!string.IsNullOrEmpty(dto.InspectionType))
                    entity.InspectionType = dto.InspectionType;
                if (!string.IsNullOrEmpty(dto.Status))
                    entity.Status = dto.Status;

                // 外键
                if (dto.InspectorId.HasValue)
                    entity.InspectorId = dto.InspectorId;

                // 质检信息
                if (dto.TestedQuantity.HasValue)
                    entity.TestedQuantity = dto.TestedQuantity;
                if (dto.QualifiedQuantity.HasValue)
                    entity.QualifiedQuantity = dto.QualifiedQuantity;
                if (dto.UnqualifiedQuantity.HasValue)
                    entity.UnqualifiedQuantity = dto.UnqualifiedQuantity;
                if (dto.InspectionTime.HasValue)
                    entity.InspectionTime = dto.InspectionTime;
                if (!string.IsNullOrEmpty(dto.OverallResult))
                    entity.OverallResult = dto.OverallResult;
                if (!string.IsNullOrEmpty(dto.Remark))
                    entity.Remark = dto.Remark;

                // 审计字段会由BaseEntity自动处理

                await _workReportInspectionRepository.UpdateAsync(entity);

                // 重新查询以获取关联数据
                var result = await GetByIdAsync(dto.Id);
                return result;
            }
            catch (Exception ex)
            {
                return ApiResult<GetWorkReportInspectionDto>.Fail(
                    $"更新质检记录失败: {ex.Message}", 
                    ResultCode.Error);
            }
        }

        /// <summary>
        /// 删除质检记录
        /// </summary>
        /// <param name="id">质检记录ID</param>
        /// <returns>删除结果</returns>
        public async Task<ApiResult<bool>> DeleteAsync(Guid id)
        {
            try
            {
                var entity = await _workReportInspectionRepository.GetByIdAsync(id);
                if (entity == null)
                {
                    return ApiResult<bool>.Fail("质检记录不存在", ResultCode.NotFound);
                }

                await _workReportInspectionRepository.DeleteByIdAsync(id);
                return ApiResult<bool>.Success(true, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail(
                    $"删除质检记录失败: {ex.Message}", 
                    ResultCode.Error);
            }
        }

        /// <summary>
        /// 批量删除质检记录
        /// </summary>
        /// <param name="ids">质检记录ID列表</param>
        /// <returns>删除结果</returns>
        public async Task<ApiResult<bool>> BatchDeleteAsync(List<Guid> ids)
        {
            try
            {
                if (ids == null || !ids.Any())
                {
                    return ApiResult<bool>.Fail("请选择要删除的记录", ResultCode.Error);
                }

                await _workReportInspectionRepository.DeleteByIdsAsync(ids.ToArray());
                return ApiResult<bool>.Success(true, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail(
                    $"批量删除质检记录失败: {ex.Message}", 
                    ResultCode.Error);
            }
        }

        /// <summary>
        /// 获取所有质检记录（不分页）
        /// </summary>
        /// <returns>所有质检记录列表</returns>
        public async Task<ApiResult<List<GetWorkReportInspectionDto>>> GetAllAsync()
        {
            try
            {
                var entities = await _workReportInspectionRepository.AsQueryable()
                    .Includes(x => x.Product)
                    .Includes(x => x.ProcessStep)
                    .Includes(x => x.Station)
                    .Includes(x => x.Team)
                    .Includes(x => x.Reporter)
                    .Includes(x => x.Inspector)
                    .Includes(x => x.Task)
                    .OrderByDescending(x => x.ReportTime)
                    .ToListAsync();

                var dtos = entities.Select(entity => new GetWorkReportInspectionDto
                {
                    Id = entity.Id,
                    InspectionCode = entity.InspectionCode,
                    InspectionName = entity.InspectionName,
                    InspectionType = entity.InspectionType,
                    Status = entity.Status,

                    // 外键
                    ProductId = entity.ProductId,
                    ProcessStepId = entity.ProcessStepId,
                    StationId = entity.StationId,
                    TeamId = entity.TeamId,
                    ReporterId = entity.ReporterId,
                    InspectorId = entity.InspectorId,
                    TaskId = entity.TaskId,

                    // 关联信息
                    ProductName = entity.Product?.MaterialName,
                    ProductCode = entity.Product?.MaterialNumber,
                    ProcessStepName = entity.ProcessStep?.ProcessStepName,
                    StationName = entity.Station?.StationName,
                    TeamName = entity.Team?.TeamName,
                    ReporterName = entity.Reporter?.DisplayName,
                    InspectorName = entity.Inspector?.DisplayName,
                    TaskName = entity.Task?.TaskName,
                    TaskCode = entity.Task?.TaskNumber,

                    // 报工和质检信息
                    ReportedQuantity = entity.ReportedQuantity,
                    ReportTime = entity.ReportTime,
                    TestedQuantity = entity.TestedQuantity,
                    QualifiedQuantity = entity.QualifiedQuantity,
                    UnqualifiedQuantity = entity.UnqualifiedQuantity,
                    InspectionTime = entity.InspectionTime,
                    OverallResult = entity.OverallResult,
                    Remark = entity.Remark
                }).ToList();

                return ApiResult<List<GetWorkReportInspectionDto>>.Success(dtos, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<List<GetWorkReportInspectionDto>>.Fail(
                    $"获取质检记录列表失败: {ex.Message}", 
                    ResultCode.Error);
            }
        }

        /// <summary>
        /// 获取质检记录列表
        /// </summary>
        /// <param name="searchDto">搜索条件</param>
        /// <returns>质检记录列表</returns>
        public async Task<ApiResult<PageResult<List<GetWorkReportInspectionDto>>>> GetListAsync(GetWorkReportInspectionSearchDto searchDto)
        {
            return await GetPagedListAsync(searchDto.InspectionCode, searchDto.InspectionName, searchDto.Status, searchDto.PageIndex, searchDto.PageSize);
        }

        /// <summary>
        /// 获取质检记录列表 (高级搜索，支持关联表字段搜索)
        /// </summary>
        /// <param name="searchDto">高级搜索条件</param>
        /// <returns>质检记录列表</returns>
        public async Task<ApiResult<PageResult<List<GetWorkReportInspectionDto>>>> GetAdvancedListAsync(WorkReportInspectionAdvancedSearchDto searchDto)
        {
            try
            {
                var query = _workReportInspectionRepository.AsQueryable();

                // 包含关联数据
                query = query.Includes(x => x.Product)
                           .Includes(x => x.ProcessStep)
                           .Includes(x => x.Station)
                           .Includes(x => x.Team)
                           .Includes(x => x.Reporter)
                           .Includes(x => x.Inspector)
                           .Includes(x => x.Task);

                // 基础条件过滤
                if (!string.IsNullOrEmpty(searchDto.InspectionCode))
                    query = query.Where(x => x.InspectionCode.Contains(searchDto.InspectionCode));
                if (!string.IsNullOrEmpty(searchDto.InspectionName))
                    query = query.Where(x => x.InspectionName.Contains(searchDto.InspectionName));
                if (!string.IsNullOrEmpty(searchDto.InspectionType))
                    query = query.Where(x => x.InspectionType == searchDto.InspectionType);
                if (!string.IsNullOrEmpty(searchDto.Status))
                    query = query.Where(x => x.Status == searchDto.Status);
                if (!string.IsNullOrEmpty(searchDto.OverallResult))
                    query = query.Where(x => x.OverallResult == searchDto.OverallResult);

                // 关联表条件过滤
                if (!string.IsNullOrEmpty(searchDto.ProductName))
                    query = query.Where(x => x.Product != null && x.Product.MaterialName.Contains(searchDto.ProductName));
                if (!string.IsNullOrEmpty(searchDto.ProductCode))
                    query = query.Where(x => x.Product != null && x.Product.MaterialNumber.Contains(searchDto.ProductCode));
                if (!string.IsNullOrEmpty(searchDto.ProcessStepName))
                    query = query.Where(x => x.ProcessStep != null && x.ProcessStep.ProcessStepName.Contains(searchDto.ProcessStepName));
                if (!string.IsNullOrEmpty(searchDto.StationName))
                    query = query.Where(x => x.Station != null && x.Station.StationName.Contains(searchDto.StationName));
                if (!string.IsNullOrEmpty(searchDto.TeamName))
                    query = query.Where(x => x.Team != null && x.Team.TeamName.Contains(searchDto.TeamName));
                if (!string.IsNullOrEmpty(searchDto.ReporterName))
                    query = query.Where(x => x.Reporter != null && x.Reporter.DisplayName.Contains(searchDto.ReporterName));
                if (!string.IsNullOrEmpty(searchDto.InspectorName))
                    query = query.Where(x => x.Inspector != null && x.Inspector.DisplayName.Contains(searchDto.InspectorName));

                // 时间范围过滤
                if (searchDto.ReportTimeStart.HasValue)
                    query = query.Where(x => x.ReportTime >= searchDto.ReportTimeStart.Value);
                if (searchDto.ReportTimeEnd.HasValue)
                    query = query.Where(x => x.ReportTime <= searchDto.ReportTimeEnd.Value);
                if (searchDto.InspectionTimeStart.HasValue)
                    query = query.Where(x => x.InspectionTime >= searchDto.InspectionTimeStart.Value);
                if (searchDto.InspectionTimeEnd.HasValue)
                    query = query.Where(x => x.InspectionTime <= searchDto.InspectionTimeEnd.Value);

                // 分页查询
                var totalCount = await query.CountAsync();
                var entities = await query
                    .OrderByDescending(x => x.ReportTime)
                    .Skip((searchDto.PageIndex - 1) * searchDto.PageSize)
                    .Take(searchDto.PageSize)
                    .ToListAsync();

                // 转换为DTO
                var dtos = entities.Select(entity => new GetWorkReportInspectionDto
                {
                    Id = entity.Id,
                    InspectionCode = entity.InspectionCode,
                    InspectionName = entity.InspectionName,
                    InspectionType = entity.InspectionType,
                    Status = entity.Status,
                    ProductId = entity.ProductId,
                    ProcessStepId = entity.ProcessStepId,
                    StationId = entity.StationId,
                    TeamId = entity.TeamId,
                    ReporterId = entity.ReporterId,
                    InspectorId = entity.InspectorId,
                    TaskId = entity.TaskId,
                    ProductName = entity.Product?.MaterialName,
                    ProductCode = entity.Product?.MaterialNumber,
                    ProcessStepName = entity.ProcessStep?.ProcessStepName,
                    StationName = entity.Station?.StationName,
                    TeamName = entity.Team?.TeamName,
                    ReporterName = entity.Reporter?.DisplayName,
                    InspectorName = entity.Inspector?.DisplayName,
                    TaskName = entity.Task?.TaskName,
                    TaskCode = entity.Task?.TaskNumber,
                    ReportedQuantity = entity.ReportedQuantity,
                    ReportTime = entity.ReportTime,
                    TestedQuantity = entity.TestedQuantity,
                    QualifiedQuantity = entity.QualifiedQuantity,
                    UnqualifiedQuantity = entity.UnqualifiedQuantity,
                    InspectionTime = entity.InspectionTime,
                    OverallResult = entity.OverallResult,
                    Remark = entity.Remark
                }).ToList();

                var pageResult = new PageResult<List<GetWorkReportInspectionDto>>
                {
                    Data = dtos,
                    TotalCount = totalCount,
                    TotalPage = (int)Math.Ceiling((double)totalCount / searchDto.PageSize)
                };

                return ApiResult<PageResult<List<GetWorkReportInspectionDto>>>.Success(pageResult, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<PageResult<List<GetWorkReportInspectionDto>>>.Fail(
                    $"获取质检记录高级搜索列表失败: {ex.Message}", 
                    ResultCode.Error);
            }
        }

        /// <summary>
        /// 执行质检操作
        /// </summary>
        /// <param name="id">质检记录Id</param>
        /// <param name="updateDto">质检更新信息</param>
        /// <returns>质检结果</returns>
        public async Task<ApiResult<GetWorkReportInspectionDto>> PerformInspectionAsync(Guid id, UpdateWorkReportInspectionDto updateDto)
        {
            updateDto.Id = id;
            return await UpdateAsync(updateDto);
        }

        /// <summary>
        /// 获取质检统计信息
        /// </summary>
        /// <param name="searchDto">搜索条件</param>
        /// <returns>统计信息</returns>
        public async Task<ApiResult<object>> GetStatisticsAsync(WorkReportInspectionAdvancedSearchDto searchDto)
        {
            try
            {
                var query = _workReportInspectionRepository.AsQueryable();

                // 应用搜索条件（简化版本）
                if (!string.IsNullOrEmpty(searchDto.Status))
                    query = query.Where(x => x.Status == searchDto.Status);

                var totalCount = await query.CountAsync();
                var qualifiedCount = await query.Where(x => x.OverallResult == "合格").CountAsync();
                var unqualifiedCount = await query.Where(x => x.OverallResult == "不合格").CountAsync();

                var statistics = new
                {
                    TotalCount = totalCount,
                    QualifiedCount = qualifiedCount,
                    UnqualifiedCount = unqualifiedCount,
                    QualificationRate = totalCount > 0 ? Math.Round((decimal)qualifiedCount / totalCount * 100, 2) : 0
                };

                return ApiResult<object>.Success(statistics, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<object>.Fail($"获取质检统计信息失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 检查质检相关表的数据情况
        /// </summary>
        /// <returns>各关联表的数据统计</returns>
        public async Task<ApiResult<object>> CheckRelatedDataAsync()
        {
            try
            {
                var totalCount = await _workReportInspectionRepository.AsQueryable().CountAsync();
                
                var result = new
                {
                    TotalInspectionRecords = totalCount,
                    Message = "质检数据检查完成"
                };

                return ApiResult<object>.Success(result, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<object>.Fail($"检查质检相关数据失败: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 获取简单质检记录列表（不依赖关联数据）
        /// </summary>
        /// <returns>质检记录基础信息</returns>
        public async Task<ApiResult<object>> GetSimpleListAsync()
        {
            try
            {
                var entities = await _workReportInspectionRepository.AsQueryable()
                    .OrderByDescending(x => x.ReportTime)
                    .Take(100) // 限制返回数量
                    .ToListAsync();

                var result = entities.Select(x => new
                {
                    x.Id,
                    x.InspectionCode,
                    x.InspectionName,
                    x.Status,
                    x.ReportTime
                }).ToList();

                return ApiResult<object>.Success(result, ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<object>.Fail($"获取简单质检记录列表失败: {ex.Message}", ResultCode.Error);
            }
        }
    }
}