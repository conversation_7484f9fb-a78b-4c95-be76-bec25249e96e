# 🚀 AuthService 项目技术栈总结

## 📋 项目概述

**AuthService** 是一个基于 .NET 9.0 的微服务认证系统，采用洋葱架构模式，支持动态 API 管理、用户认证授权、服务发现等功能。

## 🏗️ 架构模式

### 洋葱架构 (Onion Architecture)

```
┌─────────────────────────────────────┐
│           AuthService.Api           │  ← 表示层 (Controllers, Middleware)
├─────────────────────────────────────┤
│       AuthService.Application       │  ← 应用层 (Services, Middleware)
├─────────────────────────────────────┤
│      AuthService.Infrastructure     │  ← 基础设施层 (Repositories, Database)
├─────────────────────────────────────┤
│         AuthService.Domain          │  ← 领域层 (Entities, Interfaces)
└─────────────────────────────────────┘
```

## 🛠️ 核心技术栈

### 1. 框架和运行时

- **.NET 9.0** - 最新的.NET 框架
- **ASP.NET Core Web API** - Web API 框架
- **C# 12** - 编程语言 (启用隐式 using 和可空引用类型)

### 2. 数据访问层

- **PostgreSQL 13+** - 主数据库
- **Dapper 2.1.35** - 轻量级 ORM
- **Npgsql 8.0.5** - PostgreSQL .NET 驱动
- **FluentMigrator 5.2.0** - 数据库迁移框架

### 3. 身份验证和授权

- **JWT Bearer Authentication** - JSON Web Token 认证
- **Microsoft.AspNetCore.Authentication.JwtBearer 9.0.7**
- **自定义权限系统** - 基于角色和权限的访问控制

### 4. 服务发现和注册

- **Consul 1.7.14.7** - 服务发现和配置管理
- **Kong Gateway** - API 网关 (外部依赖)
- **健康检查** - AspNetCore.HealthChecks.Consul 8.0.1

### 5. API 文档和版本控制

- **Swagger/OpenAPI** - API 文档生成
  - Swashbuckle.AspNetCore 9.0.3
  - Swashbuckle.AspNetCore.Annotations 9.0.3
- **API 版本控制**
  - Microsoft.AspNetCore.Mvc.Versioning 5.1.0
  - Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer 5.1.0

### 6. 监控和健康检查

- **AspNetCore.HealthChecks.NpgSql 8.0.2** - PostgreSQL 健康检查
- **AspNetCore.HealthChecks.Consul 8.0.1** - Consul 健康检查
- **内置日志系统** - Microsoft.Extensions.Logging

### 7. HTTP 和网络

- **Microsoft.Extensions.Http 9.0.6** - HTTP 客户端工厂
- **System.Text.Json 9.0.6** - JSON 序列化
- **CORS 支持** - 跨域资源共享

## 🗂️ 项目结构详解

### AuthService.Domain (领域层)

```
AuthService.Domain/
├── Entities/           # 实体类
│   ├── User.cs        # 用户实体
│   └── DynamicApiEndpoint.cs  # 动态API端点实体
└── Interfaces/        # 仓储接口
    ├── IUserRepository.cs
    └── IDynamicApiEndpointRepository.cs
```

### AuthService.Application (应用层)

```
AuthService.Application/
├── Services/          # 应用服务
│   └── DynamicApiService.cs
├── Middleware/        # 中间件
│   └── DynamicRoutingMiddleware.cs
└── Examples/          # 使用示例
    └── BaseRepositoryUsageExamples.cs
```

### AuthService.Infrastructure (基础设施层)

```
AuthService.Infrastructure/
├── Database/          # 数据库相关
│   ├── DatabaseInitializer.cs
│   └── MigrationRunner.cs
├── Migrations/        # 数据库迁移
│   ├── 001_CreateUsersTable.cs
│   └── 002_CreateDynamicApiEndpointsTable.cs
├── Repositories/      # 仓储实现
│   ├── BaseRepository.cs
│   ├── UserRepository.cs
│   └── DynamicApiEndpointRepository.cs
├── Extensions/        # 扩展方法
│   └── ServiceCollectionExtensions.cs
└── Services/          # 基础设施服务
    └── ConsulService.cs
```

### AuthService.Api (表示层)

```
AuthService.Api/
├── Controllers/       # API控制器
│   ├── UserController.cs
│   ├── DynamicApiController.cs
│   └── HealthController.cs
├── Models/           # API模型
│   └── DynamicApiModels.cs
├── Extensions/       # API扩展
│   └── HttpMethodExtensions.cs
└── Program.cs        # 应用入口点
```

## 🔧 关键配置

### 数据库连接

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=*************;Port=5432;Database=authservice;Username=****;Password=****;Pooling=true;Minimum Pool Size=1;Maximum Pool Size=15;Connection Timeout=30;"
  }
}
```

### JWT 配置

```json
{
  "JwtSettings": {
    "SecretKey": "your-super-secret-key-that-is-at-least-32-characters-long",
    "Issuer": "AuthService",
    "Audience": "AuthService.Api",
    "ExpirationMinutes": 60
  }
}
```

### Consul 配置

```json
{
  "Consul": {
    "Enabled": true,
    "ConsulAddress": "http://*************:8500",
    "ServiceName": "authservice",
    "ServiceId": "authservice-1",
    "ServiceAddress": "*************",
    "ServicePort": 5143,
    "HealthCheckInterval": "00:00:30"
  }
}
```

## 🚀 核心功能特性

### 1. 用户管理

- 用户注册、登录、注销
- 密码哈希和盐值加密
- 用户状态管理（激活、锁定、软删除）
- 多租户支持

### 2. 动态 API 管理

- 运行时 API 端点配置
- 路由模板匹配
- 权限控制和访问限制
- 请求/响应转换

### 3. 认证授权

- JWT Token 生成和验证
- 基于角色的访问控制(RBAC)
- 权限细粒度控制
- 多租户权限隔离

### 4. 服务发现

- Consul 服务注册
- 健康检查监控
- 服务发现和负载均衡
- 配置管理

### 5. 数据库管理

- 自动数据库创建
- 版本化迁移管理
- 事务安全保证
- 连接池优化

## 📊 性能优化

### 数据库优化

- 连接池配置 (1-15 连接)
- 精心设计的索引策略
- 软删除模式减少数据丢失
- 查询性能优化

### 内存优化

```json
{
  "MemoryOptimization": {
    "MaxConcurrentRequests": 50,
    "RequestTimeout": 30,
    "CacheSize": "64MB"
  }
}
```

### 网络优化

- HTTP 客户端工厂模式
- 连接复用和超时控制
- CORS 优化配置

## 🔍 监控和诊断

### 健康检查端点

- `/health` - 应用健康状态
- `/health/ready` - 就绪状态检查
- `/health/live` - 存活状态检查

### 日志配置

- 结构化日志记录
- 不同级别的日志控制
- 性能和错误监控

## 🛡️ 安全特性

### 数据安全

- 密码哈希存储
- SQL 注入防护 (Dapper 参数化查询)
- 输入验证和清理

### 网络安全

- JWT Token 安全
- HTTPS 支持
- CORS 安全配置
- 请求限流和超时

### 权限安全

- 细粒度权限控制
- 多租户数据隔离
- 角色基础访问控制

## 🚀 部署架构

### 容器化部署

- Docker 容器支持
- 多环境配置 (Development, Production)
- 健康检查集成

### 微服务架构

- 服务注册发现 (Consul)
- API 网关集成 (Kong)
- 负载均衡支持

### 云原生特性

- 12-Factor 应用原则
- 配置外部化
- 无状态设计
- 水平扩展支持

## 📊 技术栈版本对照表

| 技术分类         | 技术名称          | 版本     | 用途说明             |
| ---------------- | ----------------- | -------- | -------------------- |
| **运行时**       | .NET              | 9.0      | 应用运行时框架       |
| **Web 框架**     | ASP.NET Core      | 9.0      | Web API 框架         |
| **数据库**       | PostgreSQL        | 13+      | 主数据库             |
| **ORM**          | Dapper            | 2.1.35   | 轻量级数据访问       |
| **数据库驱动**   | Npgsql            | 8.0.5    | PostgreSQL .NET 驱动 |
| **数据库迁移**   | FluentMigrator    | 5.2.0    | 数据库版本控制       |
| **身份验证**     | JWT Bearer        | 9.0.7    | Token 认证           |
| **服务发现**     | Consul            | 1.7.14.7 | 服务注册发现         |
| **API 文档**     | Swagger           | 9.0.3    | API 文档生成         |
| **API 版本控制** | Mvc.Versioning    | 5.1.0    | API 版本管理         |
| **健康检查**     | HealthChecks      | 8.0.2    | 服务监控             |
| **HTTP 客户端**  | HttpClientFactory | 9.0.6    | HTTP 请求管理        |
| **JSON 序列化**  | System.Text.Json  | 9.0.6    | JSON 处理            |

## 🔄 请求处理流程

### 1. 标准 API 请求流程

```
客户端 → Kong网关 → AuthService → 控制器 → 服务层 → 仓储层 → 数据库
```

### 2. 动态 API 请求流程

```
客户端 → Kong网关 → DynamicRoutingMiddleware → 路由解析 → 目标服务转发
```

### 3. 认证授权流程

```
请求 → JWT中间件 → Token验证 → 用户信息提取 → 权限检查 → 业务处理
```

### 4. 服务发现流程

```
应用启动 → Consul注册 → 健康检查 → Kong发现 → 负载均衡
```

## 🎯 核心设计模式

### 1. 洋葱架构 (Onion Architecture)

- **领域层**: 核心业务实体和接口
- **应用层**: 业务逻辑和服务编排
- **基础设施层**: 数据访问和外部服务
- **表示层**: API 控制器和中间件

### 2. 仓储模式 (Repository Pattern)

- **BaseRepository**: 通用 CRUD 操作
- **特化仓储**: 领域特定的数据访问
- **接口分离**: 依赖抽象而非具体实现

### 3. 依赖注入 (Dependency Injection)

- **服务注册**: 统一的服务配置
- **生命周期管理**: Scoped, Singleton, Transient
- **配置模式**: Options Pattern

### 4. 中间件模式 (Middleware Pattern)

- **请求管道**: 链式处理请求
- **横切关注点**: 认证、日志、异常处理
- **动态路由**: 运行时路由解析

## 🚀 性能特性

### 数据库性能

- **连接池**: 1-15 个连接的动态池
- **索引优化**: 精心设计的复合索引
- **查询优化**: Dapper 的高性能 SQL 映射
- **事务管理**: 自动事务和回滚

### 应用性能

- **异步编程**: 全面使用 async/await
- **内存优化**: 对象池和缓存策略
- **HTTP 优化**: 连接复用和超时控制
- **并发控制**: 最大 50 个并发请求

### 网络性能

- **压缩**: 响应数据压缩
- **缓存**: HTTP 缓存头设置
- **CDN 支持**: 静态资源优化
- **负载均衡**: Kong 网关分发

## 🔧 开发工具和脚本

### 数据库管理脚本

```bash
# Windows PowerShell
.\scripts\migrate-database.ps1 -Action up

# Linux/macOS
./scripts/migrate-database.sh -a up
```

### 健康检查端点

```
GET /health          # 整体健康状态
GET /health/ready    # 就绪检查
GET /health/live     # 存活检查
```

### API 文档访问

```
http://localhost:5143/swagger    # Swagger UI
http://localhost:5143/api-docs   # OpenAPI规范
```

## 📈 扩展性设计

### 水平扩展

- **无状态设计**: 支持多实例部署
- **负载均衡**: Kong 网关自动分发
- **服务发现**: Consul 自动注册新实例

### 垂直扩展

- **资源配置**: 可调整的连接池和缓存
- **性能监控**: 实时性能指标收集
- **瓶颈识别**: 数据库和网络监控

### 功能扩展

- **插件架构**: 中间件扩展机制
- **动态配置**: 运行时配置更新
- **多租户**: 租户隔离和数据分离

## 🛡️ 安全架构

### 认证安全

- **JWT Token**: 无状态认证机制
- **密码安全**: BCrypt 哈希和盐值
- **会话管理**: Token 过期和刷新

### 授权安全

- **RBAC**: 基于角色的访问控制
- **细粒度权限**: 资源级别的权限控制
- **多租户隔离**: 数据和权限隔离

### 网络安全

- **HTTPS**: 传输层加密
- **CORS**: 跨域访问控制
- **请求验证**: 输入验证和清理

### 数据安全

- **SQL 注入防护**: 参数化查询
- **数据加密**: 敏感数据加密存储
- **审计日志**: 操作记录和追踪
