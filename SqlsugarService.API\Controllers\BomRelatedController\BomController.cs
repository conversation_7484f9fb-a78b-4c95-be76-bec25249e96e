﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SqlsugarService.Application.IService.Process.BomManagement;
using SqlsugarService.Application.DTOs.Process;
using SqlsugarService.Application.Until;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SqlsugarService.API.Controllers.BomRelatedController
{
    /// <summary>
    /// BOM（物料清单）相关接口控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class BomController : ControllerBase
    {
        private readonly IBomService _bomService;
        /// <summary>
        /// 构造函数，注入 BOM 服务
        /// </summary>
        /// <param name="bomService">BOM 业务服务</param>
        public BomController(IBomService bomService)
        {
            _bomService = bomService;
        }

        /// <summary>
        /// 新增BOM
        /// </summary>
        /// <param name="dto">BOM数据传输对象</param>
        /// <returns>操作结果</returns>
        [HttpPost("add")]
        public async Task<ApiResult> AddBom([FromBody] BomDto dto)
        {
            try
            {
                return await _bomService.AddBomAsync(dto);
            }
            catch (Exception ex)
            {
                return ApiResult.Fail($"服务器异常: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 分页获取BOM列表
        /// </summary>
        /// <param name="productName">产品名称过滤</param>
        /// <param name="bomNumber">BOM编号过滤</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>分页的BOM列表</returns>
        [HttpGet("paged")]
        public async Task<ApiResult<PageResult<List<BomListDto>>>> GetBomPaged([FromQuery] string productName, [FromQuery] string bomNumber, [FromQuery] int pageIndex = 1, [FromQuery] int pageSize = 20)
        {
            try
            {
                return await _bomService.GetBomPagedAsync(productName, bomNumber, pageIndex, pageSize);
            }
            catch (Exception ex)
            {
                return ApiResult<PageResult<List<BomListDto>>>.Fail($"服务器异常: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 获取BOM详情
        /// </summary>
        /// <param name="bomId">BOM主键ID</param>
        /// <returns>BOM详情</returns>
        [HttpGet("detail/{bomId}")]
        public async Task<ApiResult<BomListDto>> GetBomById(Guid bomId)
        {
            try
            {
                return await _bomService.GetBomByIdAsync(bomId);
            }
            catch (Exception ex)
            {
                return ApiResult<BomListDto>.Fail($"服务器异常: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 为BOM添加单个明细
        /// </summary>
        /// <param name="bomId">BOM主键ID</param>
        /// <param name="itemDto">明细数据</param>
        /// <returns>操作结果</returns>
        [HttpPost("{bomId}/item")]
        public async Task<ApiResult> AddBomItem(Guid bomId, [FromBody] BomItemDto itemDto)
        {
            try
            {
                return await _bomService.AddBomItemAsync(bomId, itemDto);
            }
            catch (Exception ex)
            {
                return ApiResult.Fail($"服务器异常: {ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 批量添加BOM明细
        /// </summary>
        /// <param name="bomId">BOM主键ID</param>
        /// <param name="itemDtos">明细数据列表</param>
        /// <returns>操作结果</returns>
        [HttpPost("{bomId}/items")]
        public async Task<ApiResult> BatchAddBomItems(Guid bomId, [FromBody] List<BomItemDto> itemDtos)
        {
            try
            {
                return await _bomService.BatchAddBomItemsAsync(bomId, itemDtos);
            }
            catch (Exception ex)
            {
                return ApiResult.Fail($"服务器异常: {ex.Message}", ResultCode.Error);
            }
        }
    }
}
