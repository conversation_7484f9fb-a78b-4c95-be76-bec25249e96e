﻿using SqlsugarService.Application.DTOs.Process;
using SqlsugarService.Application.Until;
using SqlsugarService.Domain.Craftsmanship;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SqlsugarService.Application.IService.Process.ProcessComposition
{
    /// <summary>
    /// 工序组成服务接口
    /// </summary>
    public interface IProcessCompositionService
    {
        /// <summary>
        /// 新增工序组成
        /// </summary>
        /// <param name="dto">工序组成数据传输对象</param>
        /// <returns>操作结果</returns>
        Task<ApiResult> AddProcessCompositionAsync(ProcessCompositionDto dto);

        /// <summary>
        /// 分页获取工序组成列表
        /// </summary>
        /// <param name="processRouteId">工艺路线ID（可选，为空时获取所有）</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>分页的工序组成列表</returns>
        Task<ApiResult<PageResult<List<ProcessCompositionListDto>>>> GetProcessCompositionPagedAsync(
            Guid? processRouteId, int pageIndex, int pageSize);

        #region 工艺路线管理

        /// <summary>
        /// 新增工艺路线
        /// </summary>
        /// <param name="dto">工艺路线数据传输对象</param>
        /// <returns>操作结果</returns>
        Task<ApiResult> AddProcessRouteAsync(ProcessRouteDto dto);

        /// <summary>
        /// 分页获取工艺路线列表
        /// </summary>
        /// <param name="status">状态过滤（可选）</param>
        /// <param name="pageIndex">页码（从1开始）</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>分页的工艺路线列表</returns>
        Task<ApiResult<PageResult<List<ProcessRouteListDto>>>> GetProcessRoutePagedAsync(
            string? status, int pageIndex, int pageSize);

        #endregion

        #region 工序管理

        /// <summary>
        /// 新增工序
        /// </summary>
        /// <param name="dto">工序数据传输对象</param>
        /// <returns>操作结果</returns>
        Task<ApiResult> AddProcessStepAsync(ProcessStepDto dto);

        /// <summary>
        /// 分页获取工序列表
        /// </summary>
        /// <param name="status">状态过滤（可选）</param>
        /// <param name="pageIndex">页码（从1开始）</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>分页的工序列表</returns>
        Task<ApiResult<PageResult<List<ProcessStepListDto>>>> GetProcessStepPagedAsync(
            StepStatus? status, int pageIndex, int pageSize);

        #endregion

        #region 工序物料详情管理

        /// <summary>
        /// 单个添加工序物料详情
        /// </summary>
        /// <param name="dto">工序物料详情数据传输对象</param>
        /// <returns>操作结果</returns>
        Task<ApiResult> AddProcessStepMaterialDetailAsync(ProcessStepMaterialDetailDto dto);

        /// <summary>
        /// 批量添加工序物料详情
        /// </summary>
        /// <param name="batchDto">批量工序物料详情数据传输对象</param>
        /// <returns>操作结果</returns>
        Task<ApiResult> BatchAddProcessStepMaterialDetailsAsync(BatchProcessStepMaterialDetailDto batchDto);

        /// <summary>
        /// 分页获取工序物料详情列表
        /// </summary>
        /// <param name="processStepId">工序ID（可选，为空时获取所有）</param>
        /// <param name="ioType">投入产出类型（可选）</param>
        /// <param name="pageIndex">页码（从1开始）</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>分页的工序物料详情列表</returns>
        Task<ApiResult<PageResult<List<ProcessStepMaterialDetailListDto>>>> GetProcessStepMaterialDetailsPagedAsync(
            Guid? processStepId, MaterialIOType? ioType, int pageIndex, int pageSize);

        #endregion
    }
}
