# 质检实体关联架构设计

## 概述

根据您的要求，重新设计了报工质检实体架构，**移除了冗余的静态字段，改为通过外键关联其他实体来动态获取显示信息**。这种设计符合数据库规范化原则，避免数据冗余，确保数据一致性。

## 🏗️ 架构设计原则

### 1. 数据规范化
- **移除冗余字段**：删除了所有静态存储的关联信息字段
- **外键关联**：只保留必要的外键ID字段
- **动态获取**：通过导航属性和联表查询获取关联信息

### 2. 实体关联关系
```
WorkReportInspectionEntity (质检实体)
├── ProductId → ProductEntity (产品信息)
├── ProcessStepId → ProcessStep (工序信息)  
├── StationId → StationEntity (站点信息)
├── TeamId → TeamEntity (班组信息)
├── ReporterId → Users (报工人员)
├── InspectorId → Users (检验人员)
├── WorkOrderId → ProductionOrder (生产工单)
└── TaskId → WorkOrderTaskEntity (工单任务)
```

## 📋 实体结构

### WorkReportInspectionEntity (核心质检实体)

```csharp
public class WorkReportInspectionEntity : BaseEntity
{
    // 主键
    public Guid Id { get; set; }
    
    // 基础信息
    public string InspectionCode { get; set; }      // 检验单号
    public string InspectionName { get; set; }      // 检验单名称
    public string InspectionType { get; set; }      // 检验类型
    public string Status { get; set; }              // 状态
    
    // 外键关联 (只存储ID)
    public Guid ProductId { get; set; }             // 产品ID
    public Guid ProcessStepId { get; set; }         // 工序ID
    public Guid StationId { get; set; }             // 站点ID
    public Guid TeamId { get; set; }                // 班组ID
    public Guid ReporterId { get; set; }            // 报工人员ID
    public Guid? InspectorId { get; set; }          // 检验人员ID
    public Guid? WorkOrderId { get; set; }          // 工单ID
    public Guid? TaskId { get; set; }               // 任务ID
    
    // 导航属性 (用于联表查询)
    public ProductEntity? Product { get; set; }
    public ProcessStep? ProcessStep { get; set; }
    public StationEntity? Station { get; set; }
    public TeamEntity? Team { get; set; }
    public Users? Reporter { get; set; }
    public Users? Inspector { get; set; }
    public ProductionOrder? WorkOrder { get; set; }
    public WorkOrderTaskEntity? Task { get; set; }
    
    // 质检业务数据
    public int ReportedQuantity { get; set; }       // 报工数量
    public DateTime ReportTime { get; set; }        // 报工时间
    public DateTime? InspectionTime { get; set; }   // 检验时间
    public string? InspectionDepartment { get; set; } // 检验部门
    public int? TestedQuantity { get; set; }        // 检测数量
    public int? QualifiedQuantity { get; set; }     // 合格数量
    public int? UnqualifiedQuantity { get; set; }   // 不合格数量
    public string? OverallResult { get; set; }      // 检测结果
    public string? Remark { get; set; }             // 备注
}
```

## 🔗 关联实体字段映射

### 显示字段与实体字段对应关系

| 效果图显示字段 | 来源实体 | 实体字段 | 说明 |
|---------------|----------|----------|------|
| 产品名称 | ProductEntity | MaterialName | 产品主数据 |
| 产品编号 | ProductEntity | MaterialNumber | 产品编码 |
| 工序名称 | ProcessStep | ProcessStepName | 工艺工序 |
| 站点名称 | StationEntity | StationName | 作业站点 |
| 班组名称 | TeamEntity | TeamName | 作业班组 |
| 报工人员 | Users | DisplayName/Username | 用户信息 |
| 检验人员 | Users | DisplayName/Username | 用户信息 |
| 工单名称 | ProductionOrder | OrderName | 生产工单 |
| 工单编号 | ProductionOrder | OrderNumber | 工单编码 |
| 任务名称 | WorkOrderTaskEntity | TaskName | 工单任务 |
| 任务编号 | WorkOrderTaskEntity | TaskNumber | 任务编码 |

## 🔍 查询实现

### 1. 基础查询 (简单搜索)
```csharp
// 使用基础仓储方法，适用于简单的ID和基础字段搜索
var (entities, totalCount) = await _repository.GetPagedAsync(
    pageIndex, pageSize, whereExpression, orderBy);
```

### 2. 高级查询 (关联表搜索)
```csharp
// 使用联表查询，支持通过关联实体字段搜索
var query = _repository.AsQueryable()
    .Includes(x => x.Product)      // 包含产品信息
    .Includes(x => x.ProcessStep)  // 包含工序信息
    .Includes(x => x.Station)      // 包含站点信息
    .Includes(x => x.Team)         // 包含班组信息
    .Includes(x => x.Reporter)     // 包含报工人员信息
    .Includes(x => x.Inspector)    // 包含检验人员信息
    .Includes(x => x.WorkOrder)    // 包含工单信息
    .Includes(x => x.Task);        // 包含任务信息

// 支持通过关联表字段搜索
if (!string.IsNullOrWhiteSpace(searchDto.ProductName))
{
    query = query.Where(x => x.Product.MaterialName.Contains(searchDto.ProductName));
}
```

### 3. DTO转换 (动态填充关联信息)
```csharp
var dto = new GetWorkReportInspectionDto
{
    // 基础信息
    Id = entity.Id,
    InspectionCode = entity.InspectionCode,
    
    // 外键ID
    ProductId = entity.ProductId,
    
    // 通过导航属性动态获取关联信息
    ProductName = entity.Product?.MaterialName,
    ProductCode = entity.Product?.MaterialNumber,
    ProcessStepName = entity.ProcessStep?.ProcessStepName,
    StationName = entity.Station?.StationName,
    TeamName = entity.Team?.TeamName,
    ReporterName = entity.Reporter?.DisplayName ?? entity.Reporter?.Username,
    InspectorName = entity.Inspector?.DisplayName ?? entity.Inspector?.Username,
    WorkOrderName = entity.WorkOrder?.OrderName,
    WorkOrderCode = entity.WorkOrder?.OrderNumber,
    TaskName = entity.Task?.TaskName,
    TaskCode = entity.Task?.TaskNumber,
    
    // 质检数据
    ReportedQuantity = entity.ReportedQuantity,
    ReportTime = entity.ReportTime,
    // ... 其他字段
};
```

## 🚀 API接口

### 1. 基础接口
- `POST /api/WorkReportInspection/list` - 基础列表查询
- `GET /api/WorkReportInspection/{id}` - 获取详情
- `POST /api/WorkReportInspection` - 创建记录
- `PUT /api/WorkReportInspection` - 更新记录
- `DELETE /api/WorkReportInspection/{id}` - 删除记录

### 2. 高级功能接口
- `POST /api/WorkReportInspection/advanced-list` - 高级搜索 (支持关联表字段)
- `POST /api/WorkReportInspection/{id}/perform-inspection` - 执行质检
- `POST /api/WorkReportInspection/batch-delete` - 批量删除
- `POST /api/WorkReportInspection/statistics` - 统计信息

### 3. 辅助接口
- `GET /api/WorkReportInspection/inspection-types` - 检验类型选项
- `GET /api/WorkReportInspection/status-options` - 状态选项
- `GET /api/WorkReportInspection/result-options` - 检测结果选项

## ✅ 优势

### 1. 数据一致性
- **单一数据源**：关联信息只在源表维护，避免数据不一致
- **实时更新**：关联信息变更时，质检记录自动反映最新数据
- **引用完整性**：通过外键约束确保数据完整性

### 2. 存储效率
- **减少冗余**：不重复存储关联信息，节省存储空间
- **规范化设计**：符合数据库第三范式要求
- **维护简单**：关联信息只需在一处维护

### 3. 查询灵活性
- **基础查询**：支持简单的ID和基础字段搜索
- **高级查询**：支持通过关联表字段进行复杂搜索
- **性能优化**：可根据需要选择是否包含关联信息

### 4. 扩展性
- **易于扩展**：新增关联实体时只需添加外键和导航属性
- **向后兼容**：不影响现有的基础查询功能
- **灵活配置**：可根据业务需要选择包含的关联信息

## 🔧 使用建议

### 1. 查询选择
- **简单列表**：使用基础查询接口，性能更好
- **详细信息**：使用高级查询接口，信息更完整
- **搜索功能**：使用高级搜索，支持关联字段搜索

### 2. 性能优化
- **按需加载**：只在需要时包含关联信息
- **索引优化**：为常用的外键字段建立索引
- **缓存策略**：对频繁查询的关联信息进行缓存

### 3. 数据维护
- **外键约束**：确保外键引用的有效性
- **级联操作**：合理设置级联删除和更新规则
- **数据迁移**：现有数据需要建立正确的关联关系

这种设计完全符合您的要求，通过关联其他实体来动态显示信息，而不是存储静态的冗余数据。
