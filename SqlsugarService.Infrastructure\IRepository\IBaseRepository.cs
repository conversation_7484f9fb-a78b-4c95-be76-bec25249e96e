﻿using SqlSugar;
using System.Linq.Expressions;

namespace SqlsugarService.Infrastructure.IRepository
{
    /// <summary>
    /// 基础仓储接口
    /// 提供通用的CRUD操作、分页查询、事务处理等功能
    /// </summary>
    /// <typeparam name="T">实体类型，必须是类且有无参构造函数</typeparam>
    public interface IBaseRepository<T> where T : class, new()
    {
        #region 高级查询
        /// <summary>
        /// 获取可查询对象，用于构建复杂查询
        /// </summary>
        /// <returns>SqlSugar查询对象</returns>
        ISugarQueryable<T> AsQueryable();

        /// <summary>
        /// 获取数据库上下文
        /// </summary>
        /// <returns>SqlSugar数据库上下文</returns>
        ISqlSugarClient GetDbContext();
        #endregion

       

        #region 查询操作
        /// <summary>
        /// 获取所有记录
        /// </summary>
        /// <returns>实体列表</returns>
        Task<List<T>> GetAllAsync();

        /// <summary>
        /// 根据ID获取单个实体
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>实体对象，不存在时返回null</returns>
        Task<T?> GetByIdAsync(object id);

        /// <summary>
        /// 根据条件获取第一个匹配的实体
        /// </summary>
        /// <param name="whereExpression">查询条件表达式</param>
        /// <returns>实体对象，不存在时返回null</returns>
        Task<T?> GetFirstAsync(Expression<Func<T, bool>> whereExpression);

        /// <summary>
        /// 根据条件获取实体列表
        /// </summary>
        /// <param name="whereExpression">查询条件表达式</param>
        /// <returns>实体列表</returns>
        Task<List<T>> GetListAsync(Expression<Func<T, bool>> whereExpression);

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="pageIndex">页码（从1开始）</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="whereExpression">查询条件表达式，为null时查询全部</param>
        /// <param name="orderBy">排序字段，默认按ID降序</param>
        /// <returns>分页数据和总记录数</returns>
        Task<(List<T> Data, int TotalCount)> GetPagedAsync(int pageIndex, int pageSize, Expression<Func<T, bool>>? whereExpression = null, string orderBy = "Id desc");
        #endregion

        #region 新增操作
        /// <summary>
        /// 插入单个实体
        /// </summary>
        /// <param name="entity">实体对象</param>
        /// <returns>是否成功</returns>
        Task<bool> InsertAsync(T entity);

        /// <summary>
        /// 批量插入实体
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <returns>是否成功</returns>
        Task<bool> InsertRangeAsync(List<T> entities);
        #endregion

        #region 删除操作
        /// <summary>
        /// 软删除（标记删除，需要实体有IsDeleted字段）
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>是否成功</returns>
        Task<bool> SoftDeleteAsync(object id);

        /// <summary>
        /// 批量软删除
        /// </summary>
        /// <param name="ids">主键ID数组</param>
        /// <returns>是否成功</returns>
        Task<bool> SoftDeleteRangeAsync(object[] ids);
        #endregion

        #region 更新操作

        /// <summary>
        /// 更新整个实体
        /// </summary>
        /// <param name="entity">实体对象</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateAsync(T entity);

        /// <summary>
        /// 批量更新实体
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateRangeAsync(List<T> entities);
        #endregion
    }
}

