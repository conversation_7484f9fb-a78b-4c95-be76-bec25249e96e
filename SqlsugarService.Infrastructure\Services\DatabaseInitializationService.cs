using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SqlsugarService.Infrastructure.DbContext;

namespace SqlsugarService.Infrastructure.Services
{
    /// <summary>
    /// 数据库初始化服务
    /// 提供手动初始化数据库表的功能
    /// </summary>
    public class DatabaseInitializationService
    {
        private readonly SqlSugarDbContext _dbContext;
        private readonly IConfiguration _configuration;
        private readonly ILogger<DatabaseInitializationService> _logger;

        public DatabaseInitializationService(
            SqlSugarDbContext dbContext,
            IConfiguration configuration,
            ILogger<DatabaseInitializationService> logger)
        {
            _dbContext = dbContext;
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// 手动初始化所有表
        /// </summary>
        /// <param name="forceRecreate">是否强制重新创建表</param>
        /// <returns></returns>
        public async Task<bool> InitializeTablesAsync(bool forceRecreate = false)
        {
            try
            {
                _logger.LogInformation("开始手动初始化数据库表...");

                // 直接调用现有DbContext的初始化方法
                // 模拟InitializeTables方法的逻辑
                var entityTypes = new[]
                {
                    typeof(SqlsugarService.Domain.Users),
                    typeof(SqlsugarService.Domain.BOM.BomInfo),
                    typeof(SqlsugarService.Domain.BOM.BomItem),
                    typeof(SqlsugarService.Domain.Craftsmanship.ProcessRouteEntity),
                    typeof(SqlsugarService.Domain.Craftsmanship.ProcessRouteStep),
                    typeof(SqlsugarService.Domain.Craftsmanship.ProcessStep),
                    typeof(SqlsugarService.Domain.Craftsmanship.ProcessStepProduct),
                    typeof(SqlsugarService.Domain.InventoryChange.Salesorder),
                    typeof(SqlsugarService.Domain.InventoryChange.SalesOutboundDetailEntity),
                    typeof(SqlsugarService.Domain.InventoryChange.SalesOutboundEntity),
                    typeof(SqlsugarService.Domain.Materials.MaterialCategory),
                    typeof(SqlsugarService.Domain.Materials.MaterialEntity),
                    typeof(SqlsugarService.Domain.Materials.ProcessStepMaterial),
                    typeof(SqlsugarService.Domain.Materials.ProductEntity),
                    typeof(SqlsugarService.Domain.Plan.ProductionOrder),
                    typeof(SqlsugarService.Domain.Plan.ProductionPlan),
                    typeof(SqlsugarService.Domain.Plan.WorkOrderTaskEntity),
                    typeof(SqlsugarService.Domain.QualityInspection.InspectionItemEntity),
                    typeof(SqlsugarService.Domain.QualityInspection.QualityInspectionPlanEntity),
                    typeof(SqlsugarService.Domain.QualityInspection.QualityPlanInspectionItemLink),
                    typeof(SqlsugarService.Domain.QualityInspection.WorkReportInspectionEntity),
                    typeof(SqlsugarService.Domain.Station.EquipmentEntity),
                    typeof(SqlsugarService.Domain.Station.StationEntity),
                    typeof(SqlsugarService.Domain.Station.ToolCategoryEntity),
                    typeof(SqlsugarService.Domain.Station.ToolEntity),
                    typeof(SqlsugarService.Domain.Team.TeamEntity),
                    typeof(SqlsugarService.Domain.Team.TeamMemberEntity),
                    typeof(SqlsugarService.Domain.Warehouse.InventoryOrderEntity),
                    typeof(SqlsugarService.Domain.Warehouse.TransferOrderEntity),
                    typeof(SqlsugarService.Domain.Warehouse.WarehouseAreaEntity),
                    typeof(SqlsugarService.Domain.Warehouse.WarehouseEntity),
                    typeof(SqlsugarService.Domain.Warehouse.WarehouseLocationEntity),

                };

                int createdCount = 0;
                int existingCount = 0;

                foreach (var entityType in entityTypes)
                {
                    try
                    {
                        var tableName = _dbContext.Db.EntityMaintenance.GetTableName(entityType);

                        if (forceRecreate)
                        {
                            // 强制重建
                            _dbContext.Db.CodeFirst.InitTables(entityType);
                            _logger.LogInformation($"表 {tableName} ({entityType.Name}) 已重新创建");
                            createdCount++;
                        }
                        else
                        {
                            // 仅在表不存在时创建
                            if (!_dbContext.Db.DbMaintenance.IsAnyTable(tableName))
                            {
                                _dbContext.Db.CodeFirst.InitTables(entityType);
                                _logger.LogInformation($"表 {tableName} ({entityType.Name}) 不存在，已创建");
                                createdCount++;
                            }
                            else
                            {
                                _logger.LogDebug($"表 {tableName} ({entityType.Name}) 已存在，跳过创建");
                                existingCount++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"表 {entityType.Name} 处理失败: {ex.Message}");
                    }
                }

                _logger.LogInformation($"表初始化统计 - 创建: {createdCount}, 已存在: {existingCount}");

                _logger.LogInformation("数据库表初始化完成");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据库表初始化失败");
                return false;
            }
        }

        /// <summary>
        /// 检查数据库连接
        /// </summary>
        /// <returns></returns>
        public async Task<bool> CheckDatabaseConnectionAsync()
        {
            try
            {
                await _dbContext.Db.Ado.GetDataTableAsync("SELECT 1");
                _logger.LogInformation("数据库连接正常");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据库连接失败");
                return false;
            }
        }

        /// <summary>
        /// 获取数据库中所有表的信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<string>> GetAllTablesAsync()
        {
            try
            {
                var tables = _dbContext.Db.DbMaintenance.GetTableInfoList();
                return tables.Select(t => t.Name).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取表信息失败");
                return new List<string>();
            }
        }

        /// <summary>
        /// 强制重建质检表
        /// </summary>
        /// <returns></returns>
        public async Task<bool> RecreateInspectionTableAsync()
        {
            try
            {
                _logger.LogInformation("开始重建质检表...");

                var entityType = typeof(SqlsugarService.Domain.QualityInspection.WorkReportInspectionEntity);
                var tableName = _dbContext.Db.EntityMaintenance.GetTableName(entityType);

                _logger.LogInformation($"处理表: {tableName}");

                // 删除表（如果存在）
                if (_dbContext.Db.DbMaintenance.IsAnyTable(tableName))
                {
                    _dbContext.Db.DbMaintenance.DropTable(tableName);
                    _logger.LogInformation($"表 {tableName} 已删除");
                }

                // 重新创建表
                _dbContext.Db.CodeFirst.InitTables(entityType);
                _logger.LogInformation($"表 {tableName} 已重新创建，包含所有新字段");

                _logger.LogInformation("质检表重建完成");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重建质检表失败");
                return false;
            }
        }

        /// <summary>
        /// 获取各表数据统计
        /// </summary>
        /// <returns></returns>
        public Task<object> GetDataCountAsync()
        {
            try
            {
                _logger.LogInformation("开始获取数据统计...");

                var statistics = new
                {
                    // 基础数据表
                    Users = _dbContext.Db.Queryable<SqlsugarService.Domain.Users>().Count(),
                    Products = _dbContext.Db.Queryable<SqlsugarService.Domain.Materials.ProductEntity>().Count(),
                    Materials = _dbContext.Db.Queryable<SqlsugarService.Domain.Materials.MaterialEntity>().Count(),
                    MaterialCategories = _dbContext.Db.Queryable<SqlsugarService.Domain.Materials.MaterialCategory>().Count(),

                    // 工艺相关
                    ProcessSteps = _dbContext.Db.Queryable<SqlsugarService.Domain.Craftsmanship.ProcessStep>().Count(),
                    ProcessRoutes = _dbContext.Db.Queryable<SqlsugarService.Domain.Craftsmanship.ProcessRouteEntity>().Count(),

                    // 站点和班组
                    Stations = _dbContext.Db.Queryable<SqlsugarService.Domain.Station.StationEntity>().Count(),
                    Teams = _dbContext.Db.Queryable<SqlsugarService.Domain.Team.TeamEntity>().Count(),

                    // 计划相关
                    ProductionPlans = _dbContext.Db.Queryable<SqlsugarService.Domain.Plan.ProductionPlan>().Count(),
                    WorkOrderTasks = _dbContext.Db.Queryable<SqlsugarService.Domain.Plan.WorkOrderTaskEntity>().Count(),

                    // 质检相关
                    WorkReportInspections = _dbContext.Db.Queryable<SqlsugarService.Domain.QualityInspection.WorkReportInspectionEntity>().Count(),
                    InspectionItems = _dbContext.Db.Queryable<SqlsugarService.Domain.QualityInspection.InspectionItemEntity>().Count(),
                    QualityInspectionPlans = _dbContext.Db.Queryable<SqlsugarService.Domain.QualityInspection.QualityInspectionPlanEntity>().Count(),

                    // BOM相关
                    BomInfos = _dbContext.Db.Queryable<SqlsugarService.Domain.BOM.BomInfo>().Count(),
                    BomItems = _dbContext.Db.Queryable<SqlsugarService.Domain.BOM.BomItem>().Count(),

                    // 销售相关
                    SalesOrders = _dbContext.Db.Queryable<SqlsugarService.Domain.InventoryChange.Salesorder>().Count(),

                    // 仓库相关
                    Warehouses = _dbContext.Db.Queryable<SqlsugarService.Domain.Warehouse.WarehouseEntity>().Count(),

                    // 统计时间
                    CheckedAt = DateTime.Now
                };

                _logger.LogInformation("数据统计获取完成");
                return Task.FromResult<object>(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取数据统计失败");
                throw;
            }
        }
    }
}